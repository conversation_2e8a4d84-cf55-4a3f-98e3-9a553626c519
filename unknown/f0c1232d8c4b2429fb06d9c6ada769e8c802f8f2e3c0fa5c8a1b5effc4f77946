// ===== Content Generator Class =====

class ContentGenerator {
    constructor(formData) {
        this.formData = formData;
        this.cancelled = false;
        this.paused = false;
        this.currentStep = 0;
        this.totalSteps = 5;
        
        this.prompts = {
            article: this.getArticlePrompt(),
            blog: this.getBlogPrompt(),
            social: this.getSocialPrompt(),
            email: this.getEmailPrompt(),
            product: this.getProductPrompt(),
            service: this.getServicePrompt(),
            press: this.getPressPrompt(),
            seo: this.getSEOPrompt()
        };
    }

    async generate(progressCallback) {
        try {
            this.progressCallback = progressCallback;
            
            // الخطوة 1: تحضير البرومبت
            await this.updateProgress(20, 'تحضير البرومبت المخصص...');
            const prompt = this.buildPrompt();
            
            if (this.cancelled) return null;
            
            // الخطوة 2: التحقق من الاتصال
            await this.updateProgress(40, 'التحقق من الاتصال بالذكاء الصناعي...');
            await this.delay(500);
            
            if (this.cancelled) return null;
            
            // الخطوة 3: توليد المحتوى
            await this.updateProgress(60, 'توليد المحتوى باستخدام الذكاء الصناعي...');
            const content = await window.geminiAPI.generateContent(prompt, {
                progressCallback: (progress, message) => {
                    const adjustedProgress = 60 + (progress * 0.3);
                    this.updateProgress(adjustedProgress, message);
                }
            });
            
            if (this.cancelled) return null;
            
            // الخطوة 4: تحسين المحتوى
            await this.updateProgress(90, 'تحسين وتنسيق المحتوى...');
            const optimizedContent = await this.optimizeContent(content);
            
            if (this.cancelled) return null;
            
            // الخطوة 5: الانتهاء
            await this.updateProgress(100, 'تم توليد المحتوى بنجاح!');
            
            return optimizedContent;
            
        } catch (error) {
            console.error('خطأ في توليد المحتوى:', error);
            throw error;
        }
    }

    buildPrompt() {
        const basePrompt = this.prompts[this.formData.contentType] || this.prompts.article;
        
        let prompt = basePrompt
            .replace('{profession}', this.formData.profession)
            .replace('{topic}', this.formData.topic)
            .replace('{tone}', this.getToneDescription())
            .replace('{length}', this.getLengthDescription())
            .replace('{keywords}', this.formData.keywords || 'غير محدد');
        
        // إضافة تعليمات إضافية
        prompt += this.getAdditionalInstructions();
        
        return prompt;
    }

    getArticlePrompt() {
        return `
أنت كاتب محتوى محترف ومتخصص في مجال {profession}. مهمتك هي كتابة مقال احترافي عالي الجودة حول الموضوع التالي: "{topic}".

متطلبات المقال:
- المجال/المهنة: {profession}
- الموضوع: {topic}
- النبرة: {tone}
- الطول المطلوب: {length}
- الكلمات المفتاحية: {keywords}

يجب أن يتضمن المقال:
1. عنوان جذاب ومناسب للسيو
2. مقدمة تشد انتباه القارئ
3. محتوى مقسم إلى أقسام واضحة مع عناوين فرعية
4. معلومات دقيقة ومفيدة
5. أمثلة عملية من مجال {profession}
6. خاتمة تلخص النقاط الرئيسية
7. دعوة للعمل (Call to Action) مناسبة

تعليمات مهمة:
- اكتب باللغة العربية الفصحى
- استخدم معلومات دقيقة وحديثة
- تجنب التكرار والحشو
- اجعل المحتوى قابل للقراءة والفهم
- استخدم الكلمات المفتاحية بطريقة طبيعية
`;
    }

    getBlogPrompt() {
        return `
أنت مدون محترف متخصص في مجال {profession}. اكتب مقال مدونة شخصي وتفاعلي حول "{topic}".

متطلبات مقال المدونة:
- المجال: {profession}
- الموضوع: {topic}
- النبرة: {tone}
- الطول: {length}
- الكلمات المفتاحية: {keywords}

يجب أن يتضمن:
1. عنوان جذاب وشخصي
2. مقدمة تربط مع تجربة شخصية أو قصة
3. محتوى مقسم بطريقة سهلة القراءة
4. نصائح عملية وقابلة للتطبيق
5. تجارب شخصية أو أمثلة من الواقع
6. تفاعل مع القارئ (أسئلة، دعوة للتعليق)
7. خاتمة تشجع على المشاركة والتفاعل

اجعل الأسلوب:
- ودود وقريب من القارئ
- مليء بالأمثلة العملية
- يحتوي على نصائح قابلة للتطبيق
- يشجع على التفاعل والمشاركة
`;
    }

    getSocialPrompt() {
        return `
أنت مختص في إدارة وسائل التواصل الاجتماعي لمجال {profession}. اكتب منشور تفاعلي حول "{topic}".

متطلبات المنشور:
- المجال: {profession}
- الموضوع: {topic}
- النبرة: {tone}
- الطول: {length}
- الكلمات المفتاحية: {keywords}

يجب أن يتضمن:
1. خطاف قوي في البداية
2. محتوى قيم ومفيد
3. دعوة واضحة للتفاعل
4. هاشتاجات مناسبة
5. سؤال يشجع على التعليق
6. تنسيق مناسب لوسائل التواصل

اجعل المنشور:
- جذاب ومثير للاهتمام
- قصير ومركز
- يحتوي على قيمة واضحة
- يشجع على المشاركة والتفاعل
`;
    }

    getEmailPrompt() {
        return `
أنت مختص في التسويق عبر البريد الإلكتروني لمجال {profession}. اكتب رسالة إيميل تسويقية احترافية حول "{topic}".

متطلبات الإيميل:
- المجال: {profession}
- الموضوع: {topic}
- النبرة: {tone}
- الطول: {length}
- الكلمات المفتاحية: {keywords}

يجب أن يتضمن:
1. موضوع جذاب للإيميل
2. تحية شخصية
3. مقدمة تشد الانتباه
4. محتوى قيم ومفيد
5. عرض واضح أو دعوة للعمل
6. خاتمة احترافية
7. توقيع مناسب

اجعل الإيميل:
- شخصي ومباشر
- يحتوي على قيمة واضحة
- يحفز على اتخاذ إجراء
- احترافي ومقنع
`;
    }

    getProductPrompt() {
        return `
أنت كاتب محتوى تسويقي متخصص في مجال {profession}. اكتب وصف منتج احترافي ومقنع لـ "{topic}".

متطلبات وصف المنتج:
- المجال: {profession}
- المنتج: {topic}
- النبرة: {tone}
- الطول: {length}
- الكلمات المفتاحية: {keywords}

يجب أن يتضمن:
1. عنوان جذاب للمنتج
2. وصف موجز ومثير للاهتمام
3. الفوائد والمميزات الرئيسية
4. المشاكل التي يحلها المنتج
5. مواصفات تقنية (إن وجدت)
6. شهادات أو تقييمات (افتراضية)
7. دعوة قوية للشراء

اجعل الوصف:
- مقنع ومحفز للشراء
- يركز على الفوائد وليس المميزات فقط
- يخاطب احتياجات العميل
- احترافي وموثوق
`;
    }

    getServicePrompt() {
        return `
أنت مختص في كتابة المحتوى التسويقي للخدمات في مجال {profession}. اكتب وصف خدمة احترافي لـ "{topic}".

متطلبات وصف الخدمة:
- المجال: {profession}
- الخدمة: {topic}
- النبرة: {tone}
- الطول: {length}
- الكلمات المفتاحية: {keywords}

يجب أن يتضمن:
1. عنوان واضح للخدمة
2. وصف شامل للخدمة
3. الفوائد التي يحصل عليها العميل
4. عملية تقديم الخدمة
5. الخبرة والمؤهلات
6. أمثلة على نتائج سابقة
7. كيفية البدء أو التواصل

اجعل الوصف:
- واضح ومفهوم
- يبني الثقة والمصداقية
- يوضح القيمة المضافة
- يحفز على طلب الخدمة
`;
    }

    getPressPrompt() {
        return `
أنت كاتب بيانات صحفية محترف في مجال {profession}. اكتب بيان صحفي احترافي حول "{topic}".

متطلبات البيان الصحفي:
- المجال: {profession}
- الموضوع: {topic}
- النبرة: {tone}
- الطول: {length}
- الكلمات المفتاحية: {keywords}

يجب أن يتضمن:
1. عنوان إخباري جذاب
2. تاريخ ومكان الإصدار
3. مقدمة تجيب على الأسئلة الأساسية (من، ماذا، متى، أين، لماذا)
4. تفاصيل الخبر أو الحدث
5. اقتباسات من مسؤولين أو خبراء
6. معلومات إضافية ذات صلة
7. معلومات الاتصال للاستفسارات

اجعل البيان:
- موضوعي ومهني
- يحتوي على معلومات دقيقة
- مكتوب بأسلوب إخباري
- جذاب للصحفيين والإعلاميين
`;
    }

    getSEOPrompt() {
        return `
أنت خبير في تحسين محركات البحث (SEO) ومتخصص في مجال {profession}. اكتب محتوى محسن لمحركات البحث حول "{topic}".

متطلبات المحتوى المحسن:
- المجال: {profession}
- الموضوع: {topic}
- النبرة: {tone}
- الطول: {length}
- الكلمات المفتاحية: {keywords}

يجب أن يتضمن:
1. عنوان محسن للسيو (H1)
2. عناوين فرعية محسنة (H2, H3)
3. توزيع طبيعي للكلمات المفتاحية
4. محتوى عالي الجودة ومفيد
5. روابط داخلية مقترحة
6. وصف ميتا مقترح
7. كلمات مفتاحية ذات صلة

اجعل المحتوى:
- محسن لمحركات البحث
- مفيد وقيم للقارئ
- سهل القراءة والفهم
- يجيب على أسئلة المستخدمين
`;
    }

    getToneDescription() {
        const tones = {
            professional: 'احترافي ورسمي',
            friendly: 'ودود ومألوف',
            formal: 'رسمي ومهني',
            casual: 'غير رسمي ومريح',
            persuasive: 'إقناعي ومحفز',
            informative: 'إعلامي وتعليمي'
        };
        
        return tones[this.formData.tone] || 'احترافي';
    }

    getLengthDescription() {
        const lengths = {
            'short': '200-400 كلمة (قصير)',
            'medium': '500-800 كلمة (متوسط)',
            'long': '1000-1500 كلمة (طويل)',
            'very-long': '2000+ كلمة (طويل جداً)'
        };
        
        return lengths[this.formData.contentLength] || 'متوسط';
    }

    getAdditionalInstructions() {
        return `

تعليمات إضافية مهمة:
- اكتب المحتوى باللغة العربية الفصحى
- تأكد من دقة المعلومات المقدمة
- استخدم أسلوب واضح ومفهوم
- تجنب التكرار والحشو
- اجعل المحتوى مفيد وقابل للتطبيق
- استخدم تنسيق مناسب مع عناوين فرعية
- تأكد من أن المحتوى أصلي وغير منسوخ
- اجعل المحتوى مناسب للجمهور المستهدف في مجال ${this.formData.profession}

ابدأ الكتابة الآن:
`;
    }

    async optimizeContent(content) {
        // تحسين وتنسيق المحتوى
        let optimized = content;
        
        // إزالة المقدمات غير المرغوبة
        optimized = this.removeAIIntroductions(optimized);
        
        // تحسين التنسيق
        optimized = this.improveFormatting(optimized);
        
        // إضافة تنسيق HTML بسيط
        optimized = this.addBasicHTMLFormatting(optimized);
        
        return optimized;
    }

    removeAIIntroductions(content) {
        // إزالة المقدمات الشائعة للذكاء الصناعي
        const introPatterns = [
            /^(بالطبع|طبعاً|حسناً|بكل سرور).*?\n/i,
            /^(إليك|هنا|فيما يلي).*?\n/i,
            /^(سأقوم|سأكتب|سأعرض).*?\n/i,
            /^(كما طلبت|حسب طلبك).*?\n/i
        ];
        
        let cleaned = content;
        introPatterns.forEach(pattern => {
            cleaned = cleaned.replace(pattern, '');
        });
        
        return cleaned.trim();
    }

    improveFormatting(content) {
        // تحسين التنسيق العام
        return content
            .replace(/\n{3,}/g, '\n\n') // تقليل الأسطر الفارغة المتعددة
            .replace(/^\s+/gm, '') // إزالة المسافات في بداية الأسطر
            .trim();
    }

    addBasicHTMLFormatting(content) {
        // إضافة تنسيق HTML بسيط
        return content
            .replace(/^(#{1,6})\s*(.+)$/gm, (match, hashes, title) => {
                const level = hashes.length;
                return `<h${level}>${title.trim()}</h${level}>`;
            })
            .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.+?)\*/g, '<em>$1</em>')
            .replace(/\n\n/g, '</p><p>')
            .replace(/^(?!<h|<p)(.+)$/gm, '<p>$1</p>');
    }

    async updateProgress(percentage, message) {
        if (this.progressCallback) {
            this.progressCallback(percentage, message);
        }
        
        // انتظار للسماح بالإيقاف المؤقت
        while (this.paused && !this.cancelled) {
            await this.delay(100);
        }
    }

    pause() {
        this.paused = true;
    }

    resume() {
        this.paused = false;
    }

    cancel() {
        this.cancelled = true;
        this.paused = false;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
