// ===== Gemini API Manager =====

class GeminiAPIManager {
    constructor() {
        this.apiKeys = [
            'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
            'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
            'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
            'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
            'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc',
            'AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM',
            'AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds',
            'AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk',
            'AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg',
            'AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ',
            'AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4',
            'AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo',
            'AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k',
            'AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA',
            'AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ'
        ];
        
        this.currentKeyIndex = 0;
        this.baseURL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
        this.requestCounts = new Map();
        this.lastRequestTime = new Map();
        this.maxRequestsPerMinute = 60;
        this.maxTokensPerRequest = 30000;
        
        this.initializeRequestTracking();
    }

    initializeRequestTracking() {
        this.apiKeys.forEach(key => {
            this.requestCounts.set(key, 0);
            this.lastRequestTime.set(key, 0);
        });
    }

    getCurrentApiKey() {
        return this.apiKeys[this.currentKeyIndex];
    }

    rotateApiKey() {
        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
        console.log(`تم التبديل إلى API Key رقم: ${this.currentKeyIndex + 1}`);
    }

    async findAvailableApiKey() {
        const startIndex = this.currentKeyIndex;
        
        do {
            const currentKey = this.getCurrentApiKey();
            const now = Date.now();
            const lastRequest = this.lastRequestTime.get(currentKey);
            const requestCount = this.requestCounts.get(currentKey);
            
            // إعادة تعيين العداد كل دقيقة
            if (now - lastRequest > 60000) {
                this.requestCounts.set(currentKey, 0);
            }
            
            // التحقق من توفر الـ API Key
            if (requestCount < this.maxRequestsPerMinute) {
                return currentKey;
            }
            
            this.rotateApiKey();
        } while (this.currentKeyIndex !== startIndex);
        
        throw new Error('جميع مفاتيح API وصلت للحد الأقصى من الطلبات. يرجى المحاولة لاحقاً.');
    }

    updateRequestCount(apiKey) {
        const currentCount = this.requestCounts.get(apiKey) || 0;
        this.requestCounts.set(apiKey, currentCount + 1);
        this.lastRequestTime.set(apiKey, Date.now());
    }

    splitLargeContent(content, maxLength = 25000) {
        if (content.length <= maxLength) {
            return [content];
        }
        
        const chunks = [];
        let currentChunk = '';
        const sentences = content.split(/[.!?]+/);
        
        for (const sentence of sentences) {
            if ((currentChunk + sentence).length > maxLength) {
                if (currentChunk) {
                    chunks.push(currentChunk.trim());
                    currentChunk = '';
                }
                
                // إذا كانت الجملة الواحدة أطول من الحد الأقصى
                if (sentence.length > maxLength) {
                    const words = sentence.split(' ');
                    let wordChunk = '';
                    
                    for (const word of words) {
                        if ((wordChunk + ' ' + word).length > maxLength) {
                            if (wordChunk) {
                                chunks.push(wordChunk.trim());
                                wordChunk = '';
                            }
                        }
                        wordChunk += (wordChunk ? ' ' : '') + word;
                    }
                    
                    if (wordChunk) {
                        currentChunk = wordChunk;
                    }
                } else {
                    currentChunk = sentence;
                }
            } else {
                currentChunk += (currentChunk ? '. ' : '') + sentence;
            }
        }
        
        if (currentChunk) {
            chunks.push(currentChunk.trim());
        }
        
        return chunks;
    }

    async makeRequest(prompt, retries = 3) {
        for (let attempt = 0; attempt < retries; attempt++) {
            try {
                const apiKey = await this.findAvailableApiKey();
                const url = `${this.baseURL}?key=${apiKey}`;
                
                const requestBody = {
                    contents: [{
                        parts: [{ text: prompt }]
                    }],
                    generationConfig: {
                        temperature: 0.7,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 8192,
                    },
                    safetySettings: [
                        {
                            category: "HARM_CATEGORY_HARASSMENT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_HATE_SPEECH",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        }
                    ]
                };

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                this.updateRequestCount(apiKey);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    
                    if (response.status === 429) {
                        console.log(`API Key ${apiKey} وصل للحد الأقصى، جاري التبديل...`);
                        this.rotateApiKey();
                        continue;
                    }
                    
                    if (response.status === 403) {
                        console.log(`API Key ${apiKey} غير صالح، جاري التبديل...`);
                        this.rotateApiKey();
                        continue;
                    }
                    
                    throw new Error(`HTTP ${response.status}: ${errorData.error?.message || 'خطأ غير معروف'}`);
                }

                const data = await response.json();
                
                if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                    throw new Error('استجابة غير صالحة من API');
                }

                return data.candidates[0].content.parts[0].text;

            } catch (error) {
                console.error(`المحاولة ${attempt + 1} فشلت:`, error.message);
                
                if (attempt === retries - 1) {
                    throw error;
                }
                
                // انتظار قبل المحاولة التالية
                await this.delay(1000 * (attempt + 1));
                this.rotateApiKey();
            }
        }
    }

    async generateLargeContent(prompt, progressCallback) {
        try {
            // تقسيم المحتوى إذا كان كبيراً
            const chunks = this.splitLargeContent(prompt);
            const results = [];
            
            for (let i = 0; i < chunks.length; i++) {
                if (progressCallback) {
                    const progress = Math.round(((i + 1) / chunks.length) * 100);
                    progressCallback(progress, `معالجة الجزء ${i + 1} من ${chunks.length}`);
                }
                
                const result = await this.makeRequest(chunks[i]);
                results.push(result);
                
                // انتظار قصير بين الطلبات
                if (i < chunks.length - 1) {
                    await this.delay(500);
                }
            }
            
            return results.join('\n\n');
            
        } catch (error) {
            console.error('خطأ في توليد المحتوى الكبير:', error);
            throw error;
        }
    }

    async generateContent(prompt, options = {}) {
        try {
            const {
                maxRetries = 3,
                progressCallback = null,
                splitLarge = true
            } = options;
            
            // تحقق من حجم المحتوى
            if (splitLarge && prompt.length > this.maxTokensPerRequest) {
                return await this.generateLargeContent(prompt, progressCallback);
            }
            
            if (progressCallback) {
                progressCallback(50, 'جاري توليد المحتوى...');
            }
            
            const result = await this.makeRequest(prompt, maxRetries);
            
            if (progressCallback) {
                progressCallback(100, 'تم توليد المحتوى بنجاح');
            }
            
            return result;
            
        } catch (error) {
            console.error('خطأ في توليد المحتوى:', error);
            throw new Error(`فشل في توليد المحتوى: ${error.message}`);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    getApiStatus() {
        const status = [];
        
        this.apiKeys.forEach((key, index) => {
            const requestCount = this.requestCounts.get(key) || 0;
            const lastRequest = this.lastRequestTime.get(key) || 0;
            const timeSinceLastRequest = Date.now() - lastRequest;
            
            status.push({
                index: index + 1,
                key: key.substring(0, 10) + '...',
                requestCount,
                available: requestCount < this.maxRequestsPerMinute,
                timeSinceLastRequest: Math.round(timeSinceLastRequest / 1000)
            });
        });
        
        return status;
    }

    resetApiLimits() {
        this.requestCounts.clear();
        this.lastRequestTime.clear();
        this.initializeRequestTracking();
        console.log('تم إعادة تعيين حدود API');
    }
}

// إنشاء مثيل عام لمدير API
window.geminiAPI = new GeminiAPIManager();
