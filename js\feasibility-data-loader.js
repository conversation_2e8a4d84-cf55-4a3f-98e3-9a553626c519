// Feasibility Studies Data Loader
// Loads and manages feasibility studies data

class FeasibilityDataLoader {
    constructor() {
        this.data = null;
        this.categories = null;
        this.loadData();
    }

    async loadData() {
        try {
            // Load data from the feasibility studies data function
            this.data = getFeasibilityStudiesData();
            
            // Load categories from JSON file
            const response = await fetch('data/feasibility-categories.json');
            this.categories = await response.json();
            
            console.log('Feasibility studies data loaded successfully');
            this.initializeDisplay();
        } catch (error) {
            console.error('Error loading feasibility studies data:', error);
            // Fallback to the JavaScript data
            this.data = getFeasibilityStudiesData();
            this.categories = this.data;
            this.initializeDisplay();
        }
    }

    initializeDisplay() {
        // Initialize the display once data is loaded
        if (typeof window.initializeFeasibilityDisplay === 'function') {
            window.initializeFeasibilityDisplay();
        }
    }

    getAllCategories() {
        return this.categories || this.data || {};
    }

    getCategory(categoryId) {
        const categories = this.getAllCategories();
        return categories[categoryId] || null;
    }

    getAllStudies() {
        const categories = this.getAllCategories();
        const allStudies = [];
        
        Object.keys(categories).forEach(categoryId => {
            const category = categories[categoryId];
            if (category.specializations) {
                category.specializations.forEach(study => {
                    allStudies.push({
                        ...study,
                        category: categoryId,
                        categoryName: category.name,
                        categoryIcon: category.icon
                    });
                });
            }
        });
        
        return allStudies;
    }

    searchStudies(query, categoryFilter = '', priceFilter = '') {
        const allStudies = this.getAllStudies();
        let filteredStudies = allStudies;

        // Filter by search query
        if (query && query.trim() !== '') {
            const searchTerm = query.toLowerCase().trim();
            filteredStudies = filteredStudies.filter(study => 
                study.name.toLowerCase().includes(searchTerm) ||
                study.title.toLowerCase().includes(searchTerm) ||
                study.description.toLowerCase().includes(searchTerm)
            );
        }

        // Filter by category
        if (categoryFilter && categoryFilter !== '') {
            filteredStudies = filteredStudies.filter(study => 
                study.category === categoryFilter
            );
        }

        // Filter by price range
        if (priceFilter && priceFilter !== '') {
            filteredStudies = filteredStudies.filter(study => {
                const price = study.price;
                switch (priceFilter) {
                    case '0-1000':
                        return price < 1000;
                    case '1000-3000':
                        return price >= 1000 && price <= 3000;
                    case '3000-5000':
                        return price >= 3000 && price <= 5000;
                    case '5000+':
                        return price > 5000;
                    default:
                        return true;
                }
            });
        }

        return filteredStudies;
    }

    getStudyById(studyId) {
        const allStudies = this.getAllStudies();
        return allStudies.find(study => study.id === studyId) || null;
    }

    getFeaturedStudies(limit = 6) {
        const allStudies = this.getAllStudies();
        // Sort by price (higher prices are typically more comprehensive)
        return allStudies
            .sort((a, b) => b.price - a.price)
            .slice(0, limit);
    }

    getStudiesByCategory(categoryId, limit = null) {
        const category = this.getCategory(categoryId);
        if (!category || !category.specializations) {
            return [];
        }

        let studies = category.specializations.map(study => ({
            ...study,
            category: categoryId,
            categoryName: category.name,
            categoryIcon: category.icon
        }));

        if (limit) {
            studies = studies.slice(0, limit);
        }

        return studies;
    }

    getPopularStudies(limit = 8) {
        const allStudies = this.getAllStudies();
        // Sort by a combination of factors (price range that indicates popularity)
        return allStudies
            .filter(study => study.price >= 2000 && study.price <= 5000)
            .sort((a, b) => a.price - b.price)
            .slice(0, limit);
    }

    formatPrice(price) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 0
        }).format(price);
    }

    formatDuration(duration) {
        return duration || 'حسب المشروع';
    }
}

// Create global instance
window.feasibilityDataLoader = new FeasibilityDataLoader();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FeasibilityDataLoader;
}
