// Feasibility Studies Categories Management
// Handles category display and navigation

class FeasibilityCategories {
    constructor() {
        this.currentCategory = null;
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // Category filter change
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterByCategory(e.target.value);
            });
        }
    }

    displayCategories() {
        const categoriesGrid = document.getElementById('categoriesGrid');
        if (!categoriesGrid || !window.feasibilityDataLoader) return;

        const categories = window.feasibilityDataLoader.getAllCategories();
        categoriesGrid.innerHTML = '';

        Object.keys(categories).forEach(categoryId => {
            const category = categories[categoryId];
            const categoryCard = this.createCategoryCard(categoryId, category);
            categoriesGrid.appendChild(categoryCard);
        });
    }

    createCategoryCard(categoryId, category) {
        const card = document.createElement('div');
        card.className = 'category-card';
        card.setAttribute('data-category', categoryId);
        
        const studiesCount = category.specializations ? category.specializations.length : 0;
        const priceRange = this.getCategoryPriceRange(category);
        
        card.innerHTML = `
            <div class="category-header">
                <div class="category-icon">
                    <i class="${category.icon}"></i>
                </div>
                <h3 class="category-title">${category.name}</h3>
            </div>
            <div class="category-body">
                <p class="category-description">${category.description}</p>
                <div class="category-stats">
                    <div class="stat-item">
                        <i class="fas fa-file-alt"></i>
                        <span>${studiesCount} دراسة متاحة</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>من ${priceRange.min} إلى ${priceRange.max} ريال</span>
                    </div>
                </div>
            </div>
            <div class="category-footer">
                <button class="btn btn-primary category-btn" onclick="feasibilityCategories.showCategoryStudies('${categoryId}')">
                    <i class="fas fa-arrow-left me-2"></i>
                    تصفح الدراسات
                </button>
            </div>
        `;

        // Add hover effects
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
        });

        return card;
    }

    getCategoryPriceRange(category) {
        if (!category.specializations || category.specializations.length === 0) {
            return { min: 0, max: 0 };
        }

        const prices = category.specializations.map(study => study.price);
        return {
            min: Math.min(...prices).toLocaleString('ar-SA'),
            max: Math.max(...prices).toLocaleString('ar-SA')
        };
    }

    showCategoryStudies(categoryId) {
        this.currentCategory = categoryId;
        const category = window.feasibilityDataLoader.getCategory(categoryId);
        
        if (!category) return;

        // Update page title
        this.updatePageTitle(category.name);
        
        // Get studies for this category
        const studies = window.feasibilityDataLoader.getStudiesByCategory(categoryId);
        
        // Display studies
        this.displayStudies(studies);
        
        // Update category filter
        this.updateCategoryFilter(categoryId);
        
        // Scroll to products section
        this.scrollToSection('products');
        
        // Update breadcrumb
        this.updateBreadcrumb(category.name);
    }

    displayStudies(studies) {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) return;

        productsGrid.innerHTML = '';

        if (studies.length === 0) {
            productsGrid.innerHTML = `
                <div class="col-12 text-center">
                    <div class="no-studies">
                        <i class="fas fa-folder-open fa-3x mb-3"></i>
                        <h3>لا توجد دراسات في هذه الفئة حالياً</h3>
                        <p>نعمل على إضافة المزيد من الدراسات قريباً</p>
                        <button class="btn btn-primary" onclick="feasibilityCategories.showAllStudies()">
                            عرض جميع الدراسات
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        studies.forEach(study => {
            const studyCard = this.createStudyCard(study);
            productsGrid.appendChild(studyCard);
        });
    }

    createStudyCard(study) {
        const card = document.createElement('div');
        card.className = 'product-card study-card';
        card.setAttribute('data-study-id', study.id);
        
        card.innerHTML = `
            <div class="product-header">
                <div class="product-category">
                    <i class="${study.categoryIcon || 'fas fa-chart-line'}"></i>
                    <span>${study.categoryName}</span>
                </div>
                <div class="product-price">${window.feasibilityDataLoader.formatPrice(study.price)}</div>
            </div>
            <div class="product-content">
                <h3 class="product-title">${study.name}</h3>
                <p class="product-description">${study.description}</p>
                <div class="product-details">
                    <div class="detail-item">
                        <i class="fas fa-clock"></i>
                        <span>مدة التسليم: ${study.duration || 'حسب المشروع'}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${study.locations ? study.locations.slice(0, 2).join(', ') : 'جميع المناطق'}</span>
                    </div>
                    ${study.pages ? `
                    <div class="detail-item">
                        <i class="fas fa-file-pdf"></i>
                        <span>${study.pages} صفحة</span>
                    </div>
                    ` : ''}
                </div>
                ${study.includes ? `
                <div class="product-includes">
                    <h4>تشمل الدراسة:</h4>
                    <ul>
                        ${study.includes.slice(0, 3).map(item => `<li><i class="fas fa-check"></i> ${item}</li>`).join('')}
                        ${study.includes.length > 3 ? '<li class="more-items">والمزيد...</li>' : ''}
                    </ul>
                </div>
                ` : ''}
            </div>
            <div class="product-actions">
                <button class="btn btn-primary add-to-cart-btn" onclick="addToCart('${study.id}')">
                    <i class="fas fa-cart-plus me-2"></i>
                    إضافة للسلة
                </button>
                <button class="btn btn-outline-primary details-btn" onclick="showStudyDetails('${study.id}')">
                    <i class="fas fa-eye me-2"></i>
                    التفاصيل
                </button>
            </div>
        `;

        return card;
    }

    filterByCategory(categoryId) {
        if (!categoryId || categoryId === '') {
            this.showAllStudies();
            return;
        }

        this.showCategoryStudies(categoryId);
    }

    showAllStudies() {
        this.currentCategory = null;
        const allStudies = window.feasibilityDataLoader.getAllStudies();
        this.displayStudies(allStudies);
        this.updatePageTitle('جميع دراسات الجدوى');
        this.updateCategoryFilter('');
        this.clearBreadcrumb();
    }

    updatePageTitle(title) {
        const sectionTitle = document.querySelector('#products .section-title');
        if (sectionTitle) {
            sectionTitle.textContent = title;
        }
    }

    updateCategoryFilter(categoryId) {
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.value = categoryId;
        }
    }

    updateBreadcrumb(categoryName) {
        // Create or update breadcrumb
        let breadcrumb = document.querySelector('.breadcrumb-container');
        if (!breadcrumb) {
            breadcrumb = document.createElement('div');
            breadcrumb.className = 'breadcrumb-container';
            const productsSection = document.getElementById('products');
            if (productsSection) {
                productsSection.insertBefore(breadcrumb, productsSection.firstChild);
            }
        }

        breadcrumb.innerHTML = `
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="#" onclick="feasibilityCategories.showAllStudies()">جميع الدراسات</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">${categoryName}</li>
                </ol>
            </nav>
        `;
    }

    clearBreadcrumb() {
        const breadcrumb = document.querySelector('.breadcrumb-container');
        if (breadcrumb) {
            breadcrumb.remove();
        }
    }

    scrollToSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth' });
        }
    }

    // Get category statistics
    getCategoryStats() {
        const categories = window.feasibilityDataLoader.getAllCategories();
        const stats = {};

        Object.keys(categories).forEach(categoryId => {
            const category = categories[categoryId];
            const studies = category.specializations || [];
            const prices = studies.map(study => study.price);
            
            stats[categoryId] = {
                name: category.name,
                studiesCount: studies.length,
                minPrice: prices.length > 0 ? Math.min(...prices) : 0,
                maxPrice: prices.length > 0 ? Math.max(...prices) : 0,
                avgPrice: prices.length > 0 ? prices.reduce((a, b) => a + b, 0) / prices.length : 0
            };
        });

        return stats;
    }
}

// Initialize categories manager
window.feasibilityCategories = new FeasibilityCategories();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FeasibilityCategories;
}
