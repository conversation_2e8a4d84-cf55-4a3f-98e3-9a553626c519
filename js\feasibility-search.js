// Feasibility Studies Search Functionality
// Handles search and filtering for feasibility studies

class FeasibilitySearch {
    constructor() {
        this.currentResults = [];
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(() => {
                this.performSearch();
            }, 300));
            
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }

        // Search button
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.performSearch());
        }

        // Filter changes
        const categoryFilter = document.getElementById('categoryFilter');
        const priceFilter = document.getElementById('priceFilter');
        
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => this.performSearch());
        }
        
        if (priceFilter) {
            priceFilter.addEventListener('change', () => this.performSearch());
        }
    }

    performSearch() {
        if (!window.feasibilityDataLoader) {
            console.warn('Data loader not available');
            return;
        }

        const searchInput = document.getElementById('searchInput');
        const categoryFilter = document.getElementById('categoryFilter');
        const priceFilter = document.getElementById('priceFilter');

        const query = searchInput ? searchInput.value.trim() : '';
        const category = categoryFilter ? categoryFilter.value : '';
        const price = priceFilter ? priceFilter.value : '';

        // Perform search
        this.currentResults = window.feasibilityDataLoader.searchStudies(query, category, price);
        
        // Display results
        this.displayResults(this.currentResults);
        
        // Update URL (optional)
        this.updateURL(query, category, price);
    }

    displayResults(results) {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) return;

        // Clear existing content
        productsGrid.innerHTML = '';

        // Show loading state briefly
        this.showLoadingState(productsGrid);

        setTimeout(() => {
            productsGrid.innerHTML = '';
            
            if (results.length === 0) {
                this.showNoResults(productsGrid);
                return;
            }

            // Display results
            results.forEach((study, index) => {
                const studyCard = this.createStudyCard(study);
                studyCard.style.animationDelay = `${index * 0.1}s`;
                productsGrid.appendChild(studyCard);
            });

            // Update results count
            this.updateResultsCount(results.length);
            
            // Scroll to results
            this.scrollToResults();
        }, 300);
    }

    createStudyCard(study) {
        const card = document.createElement('div');
        card.className = 'product-card study-card';
        card.style.animation = 'slideInUp 0.6s ease-out both';
        
        card.innerHTML = `
            <div class="product-header">
                <div class="product-category">
                    <i class="${study.categoryIcon || 'fas fa-chart-line'}"></i>
                    <span>${study.categoryName}</span>
                </div>
                <div class="product-price">${this.formatPrice(study.price)}</div>
            </div>
            <div class="product-content">
                <h3 class="product-title">${study.name}</h3>
                <p class="product-description">${study.description}</p>
                <div class="product-details">
                    <div class="detail-item">
                        <i class="fas fa-clock"></i>
                        <span>مدة التسليم: ${study.duration || 'حسب المشروع'}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${study.locations ? study.locations.slice(0, 2).join(', ') : 'جميع المناطق'}</span>
                    </div>
                    ${study.pages ? `
                    <div class="detail-item">
                        <i class="fas fa-file-pdf"></i>
                        <span>${study.pages} صفحة</span>
                    </div>
                    ` : ''}
                </div>
                ${study.includes ? `
                <div class="product-includes">
                    <h4>تشمل الدراسة:</h4>
                    <ul>
                        ${study.includes.slice(0, 3).map(item => `<li><i class="fas fa-check"></i> ${item}</li>`).join('')}
                        ${study.includes.length > 3 ? '<li class="more-items">والمزيد...</li>' : ''}
                    </ul>
                </div>
                ` : ''}
            </div>
            <div class="product-actions">
                <button class="btn btn-primary" onclick="addToCart('${study.id}')">
                    <i class="fas fa-cart-plus me-2"></i>
                    إضافة للسلة
                </button>
                <button class="btn btn-outline-primary" onclick="showStudyDetails('${study.id}')">
                    <i class="fas fa-eye me-2"></i>
                    التفاصيل
                </button>
            </div>
        `;

        return card;
    }

    showLoadingState(container) {
        container.innerHTML = `
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري البحث...</span>
                    </div>
                    <p class="mt-3 text-muted">جاري البحث...</p>
                </div>
            </div>
        `;
    }

    showNoResults(container) {
        container.innerHTML = `
            <div class="col-12">
                <div class="no-results text-center py-5">
                    <i class="fas fa-search fa-3x mb-3 text-muted"></i>
                    <h3>لم يتم العثور على نتائج</h3>
                    <p class="text-muted mb-4">جرب البحث بكلمات مختلفة أو تغيير الفلاتر</p>
                    <button class="btn btn-primary" onclick="feasibilitySearch.clearSearch()">
                        <i class="fas fa-refresh me-2"></i>
                        مسح البحث
                    </button>
                </div>
            </div>
        `;
    }

    updateResultsCount(count) {
        // Update section title with results count
        const sectionTitle = document.querySelector('#products .section-title');
        if (sectionTitle) {
            const baseTitle = 'دراسات الجدوى المتاحة';
            sectionTitle.textContent = count > 0 ? `${baseTitle} (${count} نتيجة)` : baseTitle;
        }
    }

    clearSearch() {
        // Clear search inputs
        const searchInput = document.getElementById('searchInput');
        const categoryFilter = document.getElementById('categoryFilter');
        const priceFilter = document.getElementById('priceFilter');

        if (searchInput) searchInput.value = '';
        if (categoryFilter) categoryFilter.value = '';
        if (priceFilter) priceFilter.value = '';

        // Show all studies
        if (window.feasibilityDataLoader) {
            const allStudies = window.feasibilityDataLoader.getAllStudies();
            this.displayResults(allStudies);
        }
    }

    scrollToResults() {
        const productsSection = document.getElementById('products');
        if (productsSection) {
            productsSection.scrollIntoView({ behavior: 'smooth' });
        }
    }

    updateURL(query, category, price) {
        // Update URL without page reload (optional feature)
        const params = new URLSearchParams();
        if (query) params.set('q', query);
        if (category) params.set('category', category);
        if (price) params.set('price', price);

        const newURL = params.toString() ? 
            `${window.location.pathname}?${params.toString()}` : 
            window.location.pathname;

        window.history.replaceState({}, '', newURL);
    }

    formatPrice(price) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 0
        }).format(price);
    }

    // Utility function for debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Advanced search features
    searchByCategory(categoryId) {
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.value = categoryId;
            this.performSearch();
        }
    }

    searchByPriceRange(priceRange) {
        const priceFilter = document.getElementById('priceFilter');
        if (priceFilter) {
            priceFilter.value = priceRange;
            this.performSearch();
        }
    }

    getSearchSuggestions(query) {
        if (!window.feasibilityDataLoader || !query) return [];

        const allStudies = window.feasibilityDataLoader.getAllStudies();
        const suggestions = [];

        allStudies.forEach(study => {
            if (study.name.toLowerCase().includes(query.toLowerCase()) ||
                study.description.toLowerCase().includes(query.toLowerCase())) {
                suggestions.push({
                    text: study.name,
                    type: 'study',
                    id: study.id
                });
            }
        });

        return suggestions.slice(0, 5); // Limit to 5 suggestions
    }
}

// Initialize search functionality
window.feasibilitySearch = new FeasibilitySearch();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FeasibilitySearch;
}
