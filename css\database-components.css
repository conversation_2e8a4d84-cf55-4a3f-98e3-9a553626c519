/* Database Store Components */

/* Enhanced Card Animations */
@keyframes cardFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Grid Layouts */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    animation: slideInUp 0.6s ease-out;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    animation: slideInUp 0.6s ease-out 0.2s both;
}

/* Category Cards */
.category-card {
    background: white;
    border-radius: var(--border-radius-xl);
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.category-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(30, 58, 138, 0.03) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.category-card:hover::before {
    transform: scaleX(1);
}

.category-card:hover::after {
    opacity: 1;
}

.category-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.category-icon {
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2.2rem;
    color: white;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.category-icon::before {
    content: '';
    position: absolute;
    inset: -3px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.category-card:hover .category-icon {
    transform: scale(1.15) rotate(5deg);
    box-shadow: 0 15px 35px rgba(30, 58, 138, 0.4);
}

.category-card:hover .category-icon::before {
    opacity: 0.2;
}

.category-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 1rem;
    transition: color 0.3s ease;
}

.category-card:hover .category-title {
    color: var(--primary-color);
}

.category-description {
    color: var(--gray-600);
    font-size: 1rem;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.category-stats {
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--gray-100);
}

.studies-count {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.studies-count::before {
    content: '📊';
    font-size: 1.1rem;
}

.category-btn {
    width: 100%;
    padding: 0.875rem 1.5rem;
    font-weight: 600;
    border-radius: var(--border-radius-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.category-btn:hover::before {
    left: 100%;
}

/* Product Cards */
.product-card {
    background: white;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--gray-200);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.02) 0%, rgba(5, 150, 105, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
}

.product-card:hover::before {
    opacity: 1;
}

.product-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.product-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    position: relative;
    z-index: 2;
}

.product-header::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 10px solid var(--accent-color);
}

.product-category {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.product-category i {
    font-size: 1rem;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    text-align: right;
    margin-top: 0.5rem;
}

.product-content {
    padding: 2rem 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

.product-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--gray-800);
    line-height: 1.3;
}

.product-description {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    flex: 1;
}

.product-details {
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--gray-700);
    font-size: 0.9rem;
}

.detail-item i {
    color: var(--accent-color);
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.product-includes {
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--gray-100);
}

.product-includes h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.product-includes h4::before {
    content: '✨';
    font-size: 1.1rem;
}

.product-includes ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.product-includes li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
    font-size: 0.9rem;
}

.product-includes li i {
    color: var(--success-color);
    font-size: 0.8rem;
}

.product-description {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.product-features {
    list-style: none;
    padding: 0;
    margin-bottom: 1.5rem;
}

.product-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
    font-size: 0.9rem;
}

.product-features i {
    color: var(--success-color);
    font-size: 0.8rem;
}

.product-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-box {
    text-align: center;
    padding: 1rem;
    background: var(--gray-100);
    border-radius: var(--border-radius);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: #fafcff;
}

.product-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.product-price {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.price-current {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.price-original {
    font-size: 1rem;
    color: var(--gray-500);
    text-decoration: line-through;
}

.price-discount {
    background: var(--success-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
}

.product-actions {
    display: flex;
    gap: 0.75rem;
    padding: 1.5rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-100);
    position: relative;
    z-index: 2;
}

.product-actions .btn {
    flex: 1;
    padding: 0.875rem 1rem;
    font-weight: 600;
    border-radius: var(--border-radius-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.product-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.product-actions .btn:hover::before {
    left: 100%;
}

.product-actions .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.product-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.4);
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

.product-actions .btn-outline-primary {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 0 2px 10px rgba(30, 58, 138, 0.1);
}

.product-actions .btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .category-card {
        padding: 2rem 1.5rem;
    }

    .category-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    .category-title {
        font-size: 1.2rem;
    }

    .product-header {
        padding: 1.5rem 1rem 1rem;
    }

    .product-content {
        padding: 1.5rem 1rem;
    }

    .product-actions {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .product-actions .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .category-card {
        padding: 1.5rem 1rem;
    }

    .category-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .category-title {
        font-size: 1.1rem;
    }

    .product-title {
        font-size: 1.1rem;
    }

    .product-price {
        font-size: 1.3rem;
    }
}

/* Loading States */
.card-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-xl);
    height: 400px;
}

/* Enhanced Hover Effects */
.category-card:hover {
    animation: cardFloat 2s ease-in-out infinite;
}

.product-card:hover .product-title {
    color: var(--primary-color);
    transition: color 0.3s ease;
}

/* Badge Styles */
.featured-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, var(--accent-color), #047857);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-lg);
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 3;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.new-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-lg);
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 3;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* Progress Bar Component */
.progress-container {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    margin: 2rem 0;
}

.progress-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.progress-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-percentage {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
}

.progress-bar-container {
    position: relative;
    background: var(--gray-200);
    border-radius: var(--border-radius);
    height: 12px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius);
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-status {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.progress-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.progress-controls .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Cart Component */
.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    background: white;
}

.cart-item-info {
    flex: 1;
}

.cart-item-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.cart-item-category {
    font-size: 0.9rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.cart-item-price {
    font-weight: 700;
    color: var(--primary-color);
}

.cart-item-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 0.25rem;
}

.quantity-btn {
    background: none;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quantity-btn:hover {
    background: var(--gray-200);
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: none;
    background: transparent;
    font-weight: 600;
}

.remove-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    width: 35px;
    height: 35px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #dc2626;
    transform: scale(1.05);
}

.cart-summary {
    background: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.summary-row.total {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--primary-color);
    border-top: 1px solid var(--gray-300);
    padding-top: 0.75rem;
    margin-top: 1rem;
}

/* Filter Component */
.filter-sidebar {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.filter-section {
    margin-bottom: 2rem;
}

.filter-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--gray-200);
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.filter-option:hover {
    background: var(--gray-100);
}

.filter-option input[type="checkbox"],
.filter-option input[type="radio"] {
    margin: 0;
}

.filter-option label {
    cursor: pointer;
    flex: 1;
    margin: 0;
}

.filter-count {
    background: var(--gray-200);
    color: var(--gray-700);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
}

.price-range-slider {
    margin: 1rem 0;
}

.price-range-slider input[type="range"] {
    width: 100%;
    margin: 0.5rem 0;
}

.price-range-display {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: var(--gray-600);
    margin-top: 0.5rem;
}

/* Notification Component */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
    z-index: 9999;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.error {
    border-left-color: var(--danger-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.notification-title {
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-message {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin: 0;
}

/* Loading Skeleton */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-300) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.skeleton-header {
    height: 60px;
    margin-bottom: 1rem;
}

.skeleton-title {
    height: 20px;
    width: 70%;
    margin-bottom: 0.5rem;
}

.skeleton-text {
    height: 16px;
    width: 100%;
    margin-bottom: 0.5rem;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-button {
    height: 40px;
    width: 120px;
    margin-top: 1rem;
}

/* Features Section */
.features-section {
    padding: 5rem 0;
    background: white;
}

.feature-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-card .feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

.feature-card h4 {
    color: var(--gray-800);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
    margin: 0;
}

/* Testimonials Section */
.testimonials-section {
    padding: 5rem 0;
    background: var(--gray-100);
}

.testimonial-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    height: 100%;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
    justify-content: center;
}

.stars i {
    color: var(--accent-color);
    font-size: 1.1rem;
}

.testimonial-content p {
    color: var(--gray-700);
    font-style: italic;
    line-height: 1.6;
    margin: 0;
    text-align: center;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.author-info {
    text-align: center;
}

.author-info h5 {
    color: var(--gray-800);
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.author-info span {
    color: var(--gray-600);
    font-size: 0.9rem;
}

/* FAQ Section */
.faq-section {
    padding: 5rem 0;
    background: white;
}

.accordion {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.accordion-item {
    border: none;
    border-bottom: 1px solid var(--gray-200);
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-button {
    background: white;
    color: var(--gray-800);
    font-weight: 600;
    padding: 1.5rem;
    border: none;
    font-family: var(--font-secondary);
    text-align: right;
    direction: rtl;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border: none;
}

.accordion-button::after {
    margin-left: auto;
    margin-right: 0;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-body {
    padding: 1.5rem;
    background: var(--gray-50);
    color: var(--gray-700);
    line-height: 1.6;
}

/* Product Modal */
.modal-xl .modal-content {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-bottom: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-title {
    font-family: var(--font-secondary);
    font-weight: 700;
}

.product-details {
    padding: 2rem;
}

.product-gallery {
    margin-bottom: 2rem;
}

.product-info {
    margin-bottom: 2rem;
}

.product-specs {
    background: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-label {
    font-weight: 600;
    color: var(--gray-700);
}

.spec-value {
    color: var(--gray-600);
}

/* View Toggle */
.view-toggle {
    display: flex;
    justify-content: center;
}

.view-toggle .btn-group {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.view-toggle .btn {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background: white;
    padding: 0.5rem 1rem;
}

.view-toggle .btn.active {
    background: var(--primary-color);
    color: white;
}

/* Products List View */
.products-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.products-list .product-card {
    display: flex;
    flex-direction: row;
    align-items: stretch;
}

.products-list .product-header {
    flex: 0 0 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.products-list .product-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.products-list .product-footer {
    flex: 0 0 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .product-stats {
        grid-template-columns: 1fr;
    }

    .product-actions {
        flex-direction: column;
    }

    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .cart-item-actions {
        width: 100%;
        justify-content: space-between;
    }

    .filter-sidebar {
        position: static;
        margin-bottom: 2rem;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }

    .feature-card {
        margin-bottom: 2rem;
    }

    .testimonial-card {
        margin-bottom: 2rem;
    }

    .products-list .product-card {
        flex-direction: column;
    }

    .products-list .product-header,
    .products-list .product-footer {
        flex: none;
    }

    .view-toggle {
        margin-top: 1rem;
    }
}
