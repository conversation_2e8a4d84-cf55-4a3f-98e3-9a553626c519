<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف المتجر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/database-store.css">
    <link rel="stylesheet" href="css/database-components.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-5">اختبار وظائف متجر قواعد البيانات</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار الفئات</h3>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="testLoadCategories()">تحميل الفئات</button>
                        <div id="categoriesTest"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار المنتجات</h3>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success mb-2" onclick="testLoadProducts()">تحميل المنتجات</button>
                        <div id="productsTest"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار عربة التسوق</h3>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-warning mb-2" onclick="testCart()">اختبار السلة</button>
                        <div id="cartTest"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار البحث</h3>
                    </div>
                    <div class="card-body">
                        <input type="text" class="form-control mb-2" id="testSearch" placeholder="ابحث عن تخصص...">
                        <button class="btn btn-info mb-2" onclick="testSearch()">اختبار البحث</button>
                        <div id="searchTest"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار الإشعارات</h3>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success me-2" onclick="testNotification('success')">نجاح</button>
                        <button class="btn btn-danger me-2" onclick="testNotification('error')">خطأ</button>
                        <button class="btn btn-warning me-2" onclick="testNotification('warning')">تحذير</button>
                        <button class="btn btn-info me-2" onclick="testNotification('info')">معلومات</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار شريط التقدم</h3>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testProgressBar()">اختبار شريط التقدم</button>
                        <div id="progressTest" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/database-main.js"></script>
    <script src="js/database-categories.js"></script>
    <script src="js/database-cart.js"></script>
    <script src="js/database-search.js"></script>
    
    <script>
        // اختبار تحميل الفئات
        function testLoadCategories() {
            const testDiv = document.getElementById('categoriesTest');
            testDiv.innerHTML = '<div class="spinner-border" role="status"></div>';
            
            setTimeout(() => {
                const categoriesCount = Object.keys(categoriesData).length;
                testDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تحميل ${categoriesCount} فئة بنجاح
                    </div>
                `;
            }, 1000);
        }
        
        // اختبار تحميل المنتجات
        function testLoadProducts() {
            const testDiv = document.getElementById('productsTest');
            testDiv.innerHTML = '<div class="spinner-border" role="status"></div>';
            
            setTimeout(() => {
                generateProductsData();
                const productsCount = productsData.length;
                testDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تحميل ${productsCount} منتج بنجاح
                    </div>
                `;
            }, 1000);
        }
        
        // اختبار عربة التسوق
        function testCart() {
            const testDiv = document.getElementById('cartTest');
            
            // إضافة منتج تجريبي
            if (productsData.length === 0) {
                generateProductsData();
            }
            
            const testProduct = productsData[0];
            addToCart(testProduct.id);
            
            testDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-shopping-cart me-2"></i>
                    تم إضافة منتج إلى السلة: ${testProduct.title}
                </div>
            `;
        }
        
        // اختبار البحث
        function testSearch() {
            const searchTerm = document.getElementById('testSearch').value;
            const testDiv = document.getElementById('searchTest');
            
            if (!searchTerm) {
                testDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        يرجى إدخال كلمة للبحث
                    </div>
                `;
                return;
            }
            
            if (productsData.length === 0) {
                generateProductsData();
            }
            
            const results = productsData.filter(product => 
                product.title.includes(searchTerm) || 
                product.category.includes(searchTerm)
            );
            
            testDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-search me-2"></i>
                    تم العثور على ${results.length} نتيجة للبحث عن "${searchTerm}"
                </div>
            `;
        }
        
        // اختبار الإشعارات
        function testNotification(type) {
            const messages = {
                success: { title: 'نجح!', message: 'تم تنفيذ العملية بنجاح' },
                error: { title: 'خطأ!', message: 'حدث خطأ أثناء تنفيذ العملية' },
                warning: { title: 'تحذير!', message: 'يرجى الانتباه لهذا التحذير' },
                info: { title: 'معلومات', message: 'هذه معلومة مفيدة' }
            };
            
            const msg = messages[type];
            showNotification(msg.title, msg.message, type);
        }
        
        // اختبار شريط التقدم
        function testProgressBar() {
            const container = document.getElementById('progressTest');
            const progressBar = createProgressBar(container, 'اختبار شريط التقدم');
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressBar.updateProgress(progress, `التقدم: ${progress}%`);
                
                if (progress >= 100) {
                    clearInterval(interval);
                    progressBar.complete();
                }
            }, 500);
        }
        
        // تهيئة الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل صفحة الاختبار');
            
            // اختبار سريع للوظائف الأساسية
            setTimeout(() => {
                console.log('بدء الاختبارات التلقائية...');
                
                // اختبار تحميل البيانات
                if (typeof categoriesData !== 'undefined') {
                    console.log('✅ تم تحميل بيانات الفئات');
                } else {
                    console.log('❌ فشل في تحميل بيانات الفئات');
                }
                
                // اختبار الوظائف
                if (typeof showNotification === 'function') {
                    console.log('✅ وظيفة الإشعارات متاحة');
                } else {
                    console.log('❌ وظيفة الإشعارات غير متاحة');
                }
                
                if (typeof createProgressBar === 'function') {
                    console.log('✅ وظيفة شريط التقدم متاحة');
                } else {
                    console.log('❌ وظيفة شريط التقدم غير متاحة');
                }
                
                if (typeof addToCart === 'function') {
                    console.log('✅ وظيفة عربة التسوق متاحة');
                } else {
                    console.log('❌ وظيفة عربة التسوق غير متاحة');
                }
                
                console.log('انتهت الاختبارات التلقائية');
            }, 1000);
        });
    </script>
</body>
</html>
