// ===== Main Application Controller =====

class ContentGeneratorApp {
    constructor() {
        this.isGenerating = false;
        this.isPaused = false;
        this.currentProgress = 0;
        this.generationProcess = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.populateProfessions();
        this.initializeComponents();
    }

    setupEventListeners() {
        // Form submission
        const form = document.getElementById('contentGeneratorForm');
        form.addEventListener('submit', (e) => this.handleFormSubmit(e));

        // Progress controls
        const pauseBtn = document.getElementById('pauseBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        
        pauseBtn.addEventListener('click', () => this.togglePause());
        cancelBtn.addEventListener('click', () => this.cancelGeneration());

        // Result actions
        const copyBtn = document.getElementById('copyBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const shareBtn = document.getElementById('shareBtn');

        copyBtn.addEventListener('click', () => this.copyContent());
        downloadBtn.addEventListener('click', () => this.downloadContent());
        shareBtn.addEventListener('click', () => this.shareContent());

        // Form validation
        this.setupFormValidation();
    }

    populateProfessions() {
        const professions = [
            // المهن العربية
            'مصرف', 'مقاولات', 'ملابس', 'مهندس', 'مواشي', 'مؤسسة', 'نشر', 'وسيط',
            'احذية', 'إدارة الأعمال', 'إدارة', 'اعلان', 'الاتصالات', 'الإدارة', 'البترول',
            'التجزئة', 'التعدين', 'الحج', 'الحيوانات', 'الزراعة', 'السعودية', 'السفر',
            'السياحة', 'الصناعة', 'العقارات', 'العلاج الطبيعي', 'العلاج', 'النقل',
            'أليفة', 'بنك', 'تاجر', 'تأمين', 'تجميل', 'تحرير', 'ترجمة', 'تسويق',
            'تمريض', 'توزيع', 'توظيف', 'جمعية', 'جملة', 'حراسات', 'حلاقة', 'حيوانات',
            'دعاية', 'دواجن', 'رائد اعمال', 'شحن', 'صحافة', 'صحيف', 'صراف', 'صرافة',
            'صيانة', 'طباعة', 'طبيب', 'عقار', 'عقاري', 'عملاء', 'عيادة', 'غيار',
            'فنادق', 'قطط', 'كاتب', 'لاعب', 'متجر', 'محاسب', 'محل', 'محلات',
            'مدارس', 'مدرس', 'مدرسة', 'مدير فندق', 'مستشفى', 'مستلزمات المرأة',
            
            // المهن الإنجليزية
            'Agriculture', 'Bank', 'barber', 'CEO', 'company', 'Contracting', 'customer',
            'doctor', 'Energy', 'engineer', 'Entrepreneur', 'Financial', 'gas', 'Healthcare',
            'hotel', 'Industrial', 'institution', 'Logistics', 'manager', 'marketing',
            'Mining', 'Oil', 'paper', 'Petrochemicals', 'petroleum', 'Pharmaceutical',
            'Real Estate', 'Renewable', 'restaurant', 'Retail', 'shipping', 'store',
            'Telecom', 'Tourism', 'travel', 'Wholesale'
        ];

        const professionSelect = document.getElementById('profession');
        
        // ترتيب المهن أبجدياً
        professions.sort((a, b) => a.localeCompare(b, 'ar'));
        
        professions.forEach(profession => {
            const option = document.createElement('option');
            option.value = profession;
            option.textContent = profession;
            professionSelect.appendChild(option);
        });
    }

    setupFormValidation() {
        const form = document.getElementById('contentGeneratorForm');
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
    }

    validateField(field) {
        const value = field.value.trim();
        const isValid = value !== '';
        
        if (!isValid) {
            this.showFieldError(field, 'هذا الحقل مطلوب');
            return false;
        }
        
        // تحقق إضافي للموضوع
        if (field.id === 'topic' && value.length < 10) {
            this.showFieldError(field, 'يجب أن يكون الموضوع أكثر تفصيلاً (10 أحرف على الأقل)');
            return false;
        }
        
        this.clearFieldError(field);
        return true;
    }

    showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid');
        
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    validateForm() {
        const form = document.getElementById('contentGeneratorForm');
        const requiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        if (this.isGenerating) {
            UIComponents.showToast('تحذير', 'عملية توليد المحتوى جارية بالفعل', 'warning');
            return;
        }
        
        if (!this.validateForm()) {
            UIComponents.showToast('خطأ', 'يرجى تعبئة جميع الحقول المطلوبة بشكل صحيح', 'error');
            return;
        }
        
        const formData = this.getFormData();
        await this.startGeneration(formData);
    }

    getFormData() {
        return {
            contentType: document.getElementById('contentType').value,
            profession: document.getElementById('profession').value,
            topic: document.getElementById('topic').value,
            contentLength: document.getElementById('contentLength').value,
            tone: document.getElementById('tone').value,
            keywords: document.getElementById('keywords').value
        };
    }

    async startGeneration(formData) {
        try {
            this.isGenerating = true;
            this.isPaused = false;
            this.currentProgress = 0;
            
            this.showProgressSection();
            this.hideResultsSection();
            this.disableForm();
            
            UIComponents.showToast('بدء العملية', 'تم بدء عملية توليد المحتوى', 'info');
            
            // بدء عملية التوليد
            this.generationProcess = new ContentGenerator(formData);
            const result = await this.generationProcess.generate((progress, message) => {
                this.updateProgress(progress, message);
            });
            
            if (result && !this.generationProcess.cancelled) {
                this.showResults(result);
                UIComponents.showToast('تم بنجاح', 'تم توليد المحتوى بنجاح', 'success');
            }
            
        } catch (error) {
            console.error('خطأ في توليد المحتوى:', error);
            UIComponents.showToast('خطأ', 'حدث خطأ أثناء توليد المحتوى: ' + error.message, 'error');
        } finally {
            this.isGenerating = false;
            this.hideProgressSection();
            this.enableForm();
        }
    }

    updateProgress(percentage, message) {
        this.currentProgress = percentage;
        
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        
        progressBar.style.width = percentage + '%';
        progressText.textContent = message;
    }

    togglePause() {
        if (!this.isGenerating) return;
        
        this.isPaused = !this.isPaused;
        const pauseBtn = document.getElementById('pauseBtn');
        
        if (this.isPaused) {
            pauseBtn.innerHTML = '<i class="fas fa-play me-2"></i>استئناف';
            this.generationProcess?.pause();
            UIComponents.showToast('إيقاف مؤقت', 'تم إيقاف العملية مؤقتاً', 'warning');
        } else {
            pauseBtn.innerHTML = '<i class="fas fa-pause me-2"></i>إيقاف مؤقت';
            this.generationProcess?.resume();
            UIComponents.showToast('استئناف', 'تم استئناف العملية', 'info');
        }
    }

    cancelGeneration() {
        if (!this.isGenerating) return;
        
        if (confirm('هل أنت متأكد من إلغاء عملية توليد المحتوى؟')) {
            this.generationProcess?.cancel();
            this.isGenerating = false;
            this.hideProgressSection();
            this.enableForm();
            
            UIComponents.showToast('تم الإلغاء', 'تم إلغاء عملية توليد المحتوى', 'info');
        }
    }

    showProgressSection() {
        const progressSection = document.getElementById('progressSection');
        progressSection.style.display = 'block';
        progressSection.classList.add('fade-in');
    }

    hideProgressSection() {
        const progressSection = document.getElementById('progressSection');
        progressSection.style.display = 'none';
        progressSection.classList.remove('fade-in');
    }

    showResultsSection() {
        const resultsSection = document.getElementById('resultsSection');
        resultsSection.style.display = 'block';
        resultsSection.classList.add('fade-in');
    }

    hideResultsSection() {
        const resultsSection = document.getElementById('resultsSection');
        resultsSection.style.display = 'none';
        resultsSection.classList.remove('fade-in');
    }

    showResults(content) {
        const contentDisplay = document.getElementById('generatedContent');
        contentDisplay.innerHTML = this.formatContent(content);
        
        this.showResultsSection();
        
        // التمرير إلى النتائج
        document.getElementById('resultsSection').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    formatContent(content) {
        // تنسيق المحتوى المولد
        let formatted = content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // إضافة فقرات
        if (!formatted.startsWith('<p>')) {
            formatted = '<p>' + formatted;
        }
        if (!formatted.endsWith('</p>')) {
            formatted = formatted + '</p>';
        }
        
        return `<div class="generated-content">${formatted}</div>`;
    }

    disableForm() {
        const form = document.getElementById('contentGeneratorForm');
        const inputs = form.querySelectorAll('input, select, textarea, button');
        inputs.forEach(input => input.disabled = true);
    }

    enableForm() {
        const form = document.getElementById('contentGeneratorForm');
        const inputs = form.querySelectorAll('input, select, textarea, button');
        inputs.forEach(input => input.disabled = false);
    }

    copyContent() {
        const contentDisplay = document.getElementById('generatedContent');
        const text = contentDisplay.textContent || contentDisplay.innerText;
        
        navigator.clipboard.writeText(text).then(() => {
            UIComponents.showToast('تم النسخ', 'تم نسخ المحتوى إلى الحافظة', 'success');
        }).catch(err => {
            console.error('خطأ في النسخ:', err);
            UIComponents.showToast('خطأ', 'فشل في نسخ المحتوى', 'error');
        });
    }

    downloadContent() {
        const contentDisplay = document.getElementById('generatedContent');
        const text = contentDisplay.textContent || contentDisplay.innerText;
        
        const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'generated-content.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        
        UIComponents.showToast('تم التحميل', 'تم تحميل المحتوى بنجاح', 'success');
    }

    shareContent() {
        const contentDisplay = document.getElementById('generatedContent');
        const text = contentDisplay.textContent || contentDisplay.innerText;
        
        if (navigator.share) {
            navigator.share({
                title: 'محتوى مولد بالذكاء الصناعي',
                text: text
            }).then(() => {
                UIComponents.showToast('تم المشاركة', 'تم مشاركة المحتوى بنجاح', 'success');
            }).catch(err => {
                console.error('خطأ في المشاركة:', err);
            });
        } else {
            // نسخ الرابط كبديل
            this.copyContent();
        }
    }

    initializeComponents() {
        // تهيئة المكونات الإضافية
        UIComponents.init();
        
        // إضافة تأثيرات التحميل
        this.addLoadingEffects();
    }

    addLoadingEffects() {
        // تأثيرات التحميل للعناصر
        const cards = document.querySelectorAll('.generator-card, .results-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('fade-in');
            }, index * 100);
        });
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.app = new ContentGeneratorApp();
});
