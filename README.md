# مولد المحتوى الذكي

أداة توليد محتوى احترافية تستخدم الذكاء الصناعي لإنشاء محتوى عالي الجودة للمهن والتخصصات المختلفة.

## المميزات

### 🤖 ذكاء صناعي متقدم
- استخدام Gemini AI 2.0 Flash
- تدوير ذكي لـ 15 مفتاح API
- معالجة المحتوى الكبير تلقائياً
- إدارة حدود الطلبات والتوكن

### 📝 أنواع المحتوى المدعومة
- مقالات احترافية
- مقالات مدونة
- منشورات وسائل التواصل
- رسائل إيميل تسويقية
- أوصاف المنتجات والخدمات
- بيانات صحفية
- محتوى محسن للسيو

### 🎯 تخصص مهني
- دعم أكثر من 80 مهنة وتخصص
- برومبت مخصص لكل نوع محتوى
- نبرات متنوعة (احترافي، ودود، رسمي، إقناعي)
- أطوال محتوى مختلفة

### 🎨 تصميم احترافي
- واجهة عربية RTL
- تصميم متجاوب لجميع الأجهزة
- خطوط Cairo و Tajawal
- ألوان وتأثيرات حديثة
- بروجريس بار تفاعلي

### ⚡ أداء متقدم
- تقسيم المحتوى الكبير تلقائياً
- إيقاف مؤقت واستئناف العملية
- إلغاء العملية في أي وقت
- معالجة الأخطاء الذكية

## هيكل المشروع

```
Data X/
├── index.html                   # مولد المحتوى الذكي
├── database-store.html          # متجر قواعد البيانات الخليجية
├── css/
│   ├── styles.css              # تصميم مولد المحتوى
│   ├── components.css          # مكونات مولد المحتوى
│   ├── database-store.css      # التصميم الرئيسي للمتجر
│   └── database-components.css # مكونات تصميم المتجر
├── js/
│   ├── main.js                 # التحكم الرئيسي لمولد المحتوى
│   ├── gemini-api.js           # إدارة Gemini API
│   ├── content-generator.js    # منطق توليد المحتوى
│   ├── ui-components.js        # مكونات واجهة المستخدم
│   ├── database-main.js        # الوظائف الرئيسية للمتجر
│   ├── database-categories.js  # إدارة الفئات والمنتجات
│   ├── database-cart.js        # عربة التسوق
│   └── database-search.js      # البحث والفلترة
└── README.md                   # التوثيق
```

## 🏪 متجر قواعد البيانات الخليجية

بالإضافة إلى مولد المحتوى الذكي، يحتوي المشروع على متجر إلكتروني متكامل لبيع قواعد البيانات:

### 🌟 مميزات المتجر
- **أكثر من 50 تخصص**: طبي، هندسي، تجاري، تعليمي، مالي، سياحي، عقاري
- **قواعد بيانات شاملة**: أكثر من 100,000 جهة اتصال
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **بحث ذكي**: مع اقتراحات تلقائية وفلترة متقدمة
- **عربة تسوق تفاعلية**: إدارة المنتجات والكميات
- **عملية شراء محاكاة**: مع شريط تقدم احترافي

### 📊 الفئات المتاحة
1. **الطبية والصحية**: طبيب، مستشفى، عيادة، تمريض
2. **الهندسة والتقنية**: مهندسين، صناعية، بتروكيماويات، طاقة
3. **الأعمال والإدارة**: مدراء، رؤساء تنفيذيين، رواد أعمال
4. **التعليم والتدريب**: مدرسين، مدارس، جمعيات
5. **المالية والمصرفية**: بنوك، محاسبين، تأمين
6. **السياحة والسفر**: فنادق، وكالات سفر، سياحة
7. **العقارات**: مطورين، وسطاء، استثمار عقاري
8. **التجارة والبيع**: متاجر، مطاعم، خدمات

### 🎯 تحسين محركات البحث
- **عنوان محسن**: "متجر قواعد البيانات الخليجية - بيانات اتصال احترافية للتسويق"
- **وصف جذاب**: متوافق مع معايير السيو الحديثة
- **Schema.org**: مخطط متجر متكامل
- **مؤشرات الموثوقية**: E-E-A-T معايير Google

### 🚀 كيفية استخدام المتجر
1. افتح `database-store.html`
2. تصفح الفئات أو استخدم البحث
3. اختر المنتجات المطلوبة
4. أضف إلى عربة التسوق
5. اتبع عملية الشراء

## التقنيات المستخدمة

- **HTML5** - هيكل الصفحة
- **CSS3** - التصميم والتنسيق
- **JavaScript ES6+** - البرمجة والتفاعل
- **Bootstrap 5 RTL** - إطار العمل
- **Font Awesome** - الأيقونات
- **Google Fonts** - الخطوط العربية
- **Gemini AI API** - الذكاء الصناعي

## كيفية الاستخدام

### 1. فتح الأداة
افتح ملف `index.html` في المتصفح

### 2. تعبئة النموذج
- اختر نوع المحتوى المطلوب
- حدد المهنة أو التخصص
- اكتب الموضوع أو الفكرة
- اختر طول المحتوى والنبرة
- أضف الكلمات المفتاحية (اختياري)

### 3. توليد المحتوى
- اضغط على "توليد المحتوى"
- راقب شريط التقدم
- يمكنك الإيقاف المؤقت أو الإلغاء

### 4. النتائج
- عرض المحتوى المولد منسق
- نسخ المحتوى
- تحميل كملف نصي
- مشاركة المحتوى

## مفاتيح API

الأداة تستخدم 15 مفتاح API لـ Gemini مع تدوير ذكي:

```javascript
const apiKeys = [
    'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
    'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
    // ... المزيد
];
```

## الميزات المتقدمة

### إدارة API ذكية
- تدوير تلقائي للمفاتيح
- مراقبة حدود الطلبات
- معالجة الأخطاء والاستثناءات
- إعادة المحاولة التلقائية

### تقسيم المحتوى
- تقسيم النصوص الكبيرة تلقائياً
- معالجة حدود التوكن
- دمج النتائج بسلاسة

### تجربة المستخدم
- بروجريس بار تفاعلي
- رسائل تنبيه ملونة
- تحقق من صحة البيانات
- تنسيق المحتوى التلقائي

## التخصيص

### إضافة مهن جديدة
```javascript
// في ملف main.js
const professions = [
    'مهنة جديدة',
    // ... المهن الأخرى
];
```

### تخصيص البرومبت
```javascript
// في ملف content-generator.js
getCustomPrompt() {
    return `
    برومبت مخصص جديد...
    `;
}
```

### تعديل التصميم
```css
/* في ملف styles.css */
:root {
    --primary-color: #لون-جديد;
    /* ... المتغيرات الأخرى */
}
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في API**
   - تحقق من صحة مفاتيح API
   - تأكد من الاتصال بالإنترنت

2. **بطء في التوليد**
   - قد يكون بسبب حجم المحتوى الكبير
   - الأداة تقسم المحتوى تلقائياً

3. **مشاكل في التنسيق**
   - تأكد من دعم المتصفح للـ CSS الحديث
   - استخدم متصفح حديث

## المتطلبات

- متصفح حديث يدعم ES6+
- اتصال بالإنترنت
- مفاتيح API صالحة لـ Gemini

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## المساهمة

نرحب بالمساهمات! يمكنك:
- إضافة مهن جديدة
- تحسين البرومبت
- تطوير الواجهة
- إصلاح الأخطاء

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

---

**مولد المحتوى الذكي** - أداة احترافية لتوليد محتوى عالي الجودة بالذكاء الصناعي
