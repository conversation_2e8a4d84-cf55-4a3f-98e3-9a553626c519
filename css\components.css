/* ===== Component Specific Styles ===== */

/* ===== Toast Notifications ===== */
.toast-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 9999;
    direction: rtl;
}

.toast {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--danger-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.info {
    border-left: 4px solid var(--info-color);
}

.toast-icon {
    font-size: 1.25rem;
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--danger-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast.info .toast-icon {
    color: var(--info-color);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.toast-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.25rem;
    padding: 0;
    transition: var(--transition-fast);
}

.toast-close:hover {
    color: var(--text-primary);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== Modal Components ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 9998;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
    from {
        transform: scale(0.9);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* ===== Loading Spinner ===== */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

.spinner-sm {
    width: 20px;
    height: 20px;
    border-width: 2px;
}

.spinner-lg {
    width: 60px;
    height: 60px;
    border-width: 6px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Content Formatting ===== */
.generated-content {
    font-family: 'Tajawal', sans-serif;
    line-height: 1.8;
    color: var(--text-primary);
}

.generated-content h1,
.generated-content h2,
.generated-content h3,
.generated-content h4,
.generated-content h5,
.generated-content h6 {
    color: var(--primary-color);
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.generated-content h1 {
    font-size: 2rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.generated-content h2 {
    font-size: 1.5rem;
}

.generated-content h3 {
    font-size: 1.25rem;
}

.generated-content p {
    margin-bottom: 1.5rem;
    text-align: justify;
}

.generated-content ul,
.generated-content ol {
    margin-bottom: 1.5rem;
    padding-right: 2rem;
}

.generated-content li {
    margin-bottom: 0.5rem;
}

.generated-content blockquote {
    border-right: 4px solid var(--primary-color);
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    font-style: italic;
}

.generated-content strong {
    color: var(--primary-color);
    font-weight: 600;
}

.generated-content em {
    color: var(--text-secondary);
}

.generated-content code {
    background: var(--bg-tertiary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.generated-content pre {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--border-radius);
    overflow-x: auto;
    margin: 1.5rem 0;
}

.generated-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.generated-content th,
.generated-content td {
    padding: 0.75rem 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.generated-content th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.generated-content tr:nth-child(even) {
    background: var(--bg-secondary);
}

/* ===== Statistics Cards ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* ===== Feature Cards ===== */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.feature-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== Accordion Component ===== */
.accordion-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    overflow: hidden;
}

.accordion-header {
    background: var(--bg-secondary);
    padding: 1rem 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition-fast);
}

.accordion-header:hover {
    background: var(--bg-tertiary);
}

.accordion-title {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.accordion-icon {
    transition: var(--transition-fast);
    color: var(--primary-color);
}

.accordion-item.active .accordion-icon {
    transform: rotate(180deg);
}

.accordion-content {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.accordion-item.active .accordion-content {
    padding: 1.5rem;
    max-height: 500px;
}

/* ===== Tabs Component ===== */
.tabs-container {
    margin: 2rem 0;
}

.tabs-nav {
    display: flex;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 2rem;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    transition: var(--transition-fast);
}

.tab-button:hover {
    color: var(--primary-color);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-out;
}

.tab-content.active {
    display: block;
}

/* ===== Badge Component ===== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background: var(--primary-color);
    color: white;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

.badge-info {
    background: var(--info-color);
    color: white;
}

.badge-outline {
    background: transparent;
    border: 1px solid currentColor;
}

/* ===== Responsive Utilities ===== */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .tabs-nav {
        flex-wrap: wrap;
    }
    
    .tab-button {
        flex: 1;
        min-width: 120px;
    }
}
