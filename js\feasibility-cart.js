// Feasibility Studies Cart Management
// Handles shopping cart functionality

class FeasibilityCart {
    constructor() {
        this.cartKey = 'feasibilityCart';
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateCartDisplay();
    }

    bindEvents() {
        // Cart icon click
        const cartIcon = document.getElementById('cartIcon');
        if (cartIcon) {
            cartIcon.addEventListener('click', () => this.showCart());
        }

        // Checkout button
        const checkoutBtn = document.getElementById('checkoutBtn');
        if (checkoutBtn) {
            checkoutBtn.addEventListener('click', () => this.checkout());
        }
    }

    getCart() {
        return JSON.parse(localStorage.getItem(this.cartKey) || '[]');
    }

    saveCart(cart) {
        localStorage.setItem(this.cartKey, JSON.stringify(cart));
        this.updateCartDisplay();
    }

    addToCart(studyId) {
        const study = window.feasibilityDataLoader.getStudyById(studyId);
        if (!study) {
            this.showNotification('لم يتم العثور على الدراسة', 'error');
            return false;
        }

        let cart = this.getCart();
        
        // Check if study already exists in cart
        const existingIndex = cart.findIndex(item => item.id === studyId);
        
        if (existingIndex > -1) {
            this.showNotification('هذه الدراسة موجودة بالفعل في السلة', 'warning');
            return false;
        }

        // Add study to cart
        const cartItem = {
            id: study.id,
            name: study.name,
            price: study.price,
            category: study.categoryName,
            duration: study.duration,
            addedAt: new Date().toISOString()
        };

        cart.push(cartItem);
        this.saveCart(cart);
        this.showNotification('تم إضافة الدراسة إلى السلة بنجاح', 'success');
        return true;
    }

    removeFromCart(studyId) {
        let cart = this.getCart();
        cart = cart.filter(item => item.id !== studyId);
        this.saveCart(cart);
        this.showNotification('تم حذف الدراسة من السلة', 'info');
        this.updateCartModal();
    }

    clearCart() {
        this.saveCart([]);
        this.showNotification('تم إفراغ السلة', 'info');
        this.updateCartModal();
    }

    updateCartDisplay() {
        const cart = this.getCart();
        const cartCount = document.getElementById('cartCount');
        
        if (cartCount) {
            cartCount.textContent = cart.length;
            cartCount.style.display = cart.length > 0 ? 'flex' : 'none';
        }
    }

    showCart() {
        this.updateCartModal();
        const modal = new bootstrap.Modal(document.getElementById('cartModal'));
        modal.show();
    }

    updateCartModal() {
        const cartModalBody = document.getElementById('cartModalBody');
        if (!cartModalBody) return;

        const cart = this.getCart();
        
        if (cart.length === 0) {
            cartModalBody.innerHTML = `
                <div class="empty-cart text-center">
                    <i class="fas fa-shopping-cart fa-3x mb-3 text-muted"></i>
                    <h4>السلة فارغة</h4>
                    <p class="text-muted">لم تقم بإضافة أي دراسات جدوى بعد</p>
                    <button class="btn btn-primary" data-bs-dismiss="modal" onclick="scrollToSection('products')">
                        تصفح الدراسات
                    </button>
                </div>
            `;
            return;
        }

        const total = cart.reduce((sum, item) => sum + item.price, 0);
        
        cartModalBody.innerHTML = `
            <div class="cart-items">
                ${cart.map(item => this.createCartItemHTML(item)).join('')}
            </div>
            <div class="cart-summary">
                <div class="summary-row">
                    <span>عدد الدراسات:</span>
                    <span>${cart.length}</span>
                </div>
                <div class="summary-row total">
                    <span>المجموع الكلي:</span>
                    <span class="total-price">${this.formatPrice(total)}</span>
                </div>
            </div>
            <div class="cart-actions mt-3">
                <button class="btn btn-outline-danger" onclick="feasibilityCart.clearCart()">
                    <i class="fas fa-trash me-2"></i>
                    إفراغ السلة
                </button>
            </div>
        `;
    }

    createCartItemHTML(item) {
        return `
            <div class="cart-item" data-study-id="${item.id}">
                <div class="cart-item-content">
                    <h5 class="cart-item-title">${item.name}</h5>
                    <div class="cart-item-details">
                        <span class="cart-item-category">
                            <i class="fas fa-tag"></i>
                            ${item.category}
                        </span>
                        <span class="cart-item-duration">
                            <i class="fas fa-clock"></i>
                            ${item.duration}
                        </span>
                    </div>
                </div>
                <div class="cart-item-price">
                    <span class="price">${this.formatPrice(item.price)}</span>
                    <button class="btn btn-sm btn-outline-danger remove-btn" 
                            onclick="feasibilityCart.removeFromCart('${item.id}')"
                            title="حذف من السلة">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    }

    checkout() {
        const cart = this.getCart();
        
        if (cart.length === 0) {
            this.showNotification('السلة فارغة', 'warning');
            return;
        }

        // Create checkout summary
        const total = cart.reduce((sum, item) => sum + item.price, 0);
        const checkoutData = {
            items: cart,
            total: total,
            timestamp: new Date().toISOString()
        };

        // Here you would typically send the data to your server
        // For now, we'll just show a success message
        this.showCheckoutModal(checkoutData);
    }

    showCheckoutModal(checkoutData) {
        const modalHTML = `
            <div class="modal fade" id="checkoutModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تأكيد الطلب</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="checkout-summary">
                                <h6>ملخص الطلب:</h6>
                                <div class="order-items">
                                    ${checkoutData.items.map(item => `
                                        <div class="order-item">
                                            <span class="item-name">${item.name}</span>
                                            <span class="item-price">${this.formatPrice(item.price)}</span>
                                        </div>
                                    `).join('')}
                                </div>
                                <div class="order-total">
                                    <strong>المجموع: ${this.formatPrice(checkoutData.total)}</strong>
                                </div>
                            </div>
                            <div class="contact-form mt-4">
                                <h6>معلومات التواصل:</h6>
                                <form id="checkoutForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">الاسم الكامل</label>
                                                <input type="text" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">رقم الجوال</label>
                                                <input type="tel" class="form-control" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">ملاحظات إضافية (اختياري)</label>
                                        <textarea class="form-control" rows="3"></textarea>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="feasibilityCart.submitOrder()">
                                تأكيد الطلب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing checkout modal if any
        const existingModal = document.getElementById('checkoutModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add new modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('checkoutModal'));
        modal.show();
    }

    submitOrder() {
        const form = document.getElementById('checkoutForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Here you would submit the order to your server
        // For now, we'll just show a success message
        this.showNotification('تم إرسال طلبك بنجاح! سنتواصل معك قريباً', 'success');
        
        // Clear cart
        this.clearCart();
        
        // Close modals
        const checkoutModal = bootstrap.Modal.getInstance(document.getElementById('checkoutModal'));
        const cartModal = bootstrap.Modal.getInstance(document.getElementById('cartModal'));
        
        if (checkoutModal) checkoutModal.hide();
        if (cartModal) cartModal.hide();
    }

    formatPrice(price) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 0
        }).format(price);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} notification`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${this.getNotificationIcon(type)} me-2"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Initialize cart manager
window.feasibilityCart = new FeasibilityCart();

// Global function for adding to cart (called from HTML)
window.addToCart = function(studyId) {
    return window.feasibilityCart.addToCart(studyId);
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FeasibilityCart;
}
