// Database Store Main JavaScript

// Global variables
let currentPage = 1;
let itemsPerPage = 12;
let currentFilters = {
    category: '',
    priceRange: '',
    search: ''
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize components
    initializeNavigation();
    initializeSearch();
    initializeFilters();
    initializeCart();
    initializeAnimations();

    // Wait for database data to be loaded
    if (window.DatabaseDataLoader && window.DatabaseDataLoader.isDataLoaded()) {
        // Data already loaded
        console.log('Database data already loaded');
    } else {
        // Wait for data to load
        document.addEventListener('databaseDataLoaded', function() {
            console.log('Database data loaded event received');
        });
    }

    console.log('Database Store initialized successfully');
}

// Navigation functions
function initializeNavigation() {
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Active navigation highlighting
    window.addEventListener('scroll', updateActiveNavigation);
    
    // Mobile menu toggle
    const navbarToggler = document.querySelector('.navbar-toggler');
    if (navbarToggler) {
        navbarToggler.addEventListener('click', function() {
            const navbarCollapse = document.querySelector('.navbar-collapse');
            navbarCollapse.classList.toggle('show');
        });
    }
}

function updateActiveNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
}

function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Search functionality
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }
    
    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }
}

function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    const categoryFilter = document.getElementById('categoryFilter');
    const priceFilter = document.getElementById('priceFilter');

    currentFilters.search = searchInput ? searchInput.value.trim() : '';
    currentFilters.category = categoryFilter ? categoryFilter.value : '';
    currentFilters.priceRange = priceFilter ? priceFilter.value : '';

    currentPage = 1;

    // Call the loadProducts function from DatabaseCategories if available
    if (window.DatabaseCategories && window.DatabaseCategories.loadProducts) {
        window.DatabaseCategories.loadProducts();
    }
}

// Filter functionality
function initializeFilters() {
    const categoryFilter = document.getElementById('categoryFilter');
    const priceFilter = document.getElementById('priceFilter');
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', handleSearch);
    }
    
    if (priceFilter) {
        priceFilter.addEventListener('change', handleSearch);
    }
}

// Animation functions
function initializeAnimations() {
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.category-card, .product-card, .about-feature').forEach(el => {
        observer.observe(el);
    });
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function formatPrice(price) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0
    }).format(price);
}

function formatNumber(number) {
    return new Intl.NumberFormat('ar-SA').format(number);
}

function showNotification(title, message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    notification.innerHTML = `
        <div class="notification-header">
            <h5 class="notification-title">${title}</h5>
            <button class="notification-close" onclick="closeNotification(this)">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <p class="notification-message">${message}</p>
    `;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Auto hide after 5 seconds
    setTimeout(() => {
        closeNotification(notification.querySelector('.notification-close'));
    }, 5000);
}

function closeNotification(button) {
    const notification = button.closest('.notification');
    notification.classList.remove('show');
    setTimeout(() => {
        notification.remove();
    }, 300);
}

function showLoading(element) {
    element.classList.add('loading');
    const originalContent = element.innerHTML;
    element.innerHTML = `
        <div class="d-flex align-items-center justify-content-center">
            <div class="spinner me-2"></div>
            جاري التحميل...
        </div>
    `;
    return originalContent;
}

function hideLoading(element, originalContent) {
    element.classList.remove('loading');
    element.innerHTML = originalContent;
}

// Progress bar functions
function createProgressBar(container, title) {
    const progressHTML = `
        <div class="progress-container">
            <div class="progress-header">
                <div class="progress-title">
                    <i class="fas fa-cog fa-spin me-2"></i>
                    ${title}
                </div>
                <div class="progress-percentage">0%</div>
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar-fill" style="width: 0%"></div>
            </div>
            <div class="progress-status">بدء العملية...</div>
            <div class="progress-controls">
                <button class="btn btn-warning" onclick="pauseProgress()">
                    <i class="fas fa-pause me-2"></i>
                    إيقاف مؤقت
                </button>
                <button class="btn btn-danger" onclick="cancelProgress()">
                    <i class="fas fa-stop me-2"></i>
                    إلغاء
                </button>
            </div>
        </div>
    `;
    
    container.innerHTML = progressHTML;
    return {
        updateProgress: (percentage, status) => updateProgress(container, percentage, status),
        complete: () => completeProgress(container),
        error: (message) => errorProgress(container, message)
    };
}

function updateProgress(container, percentage, status) {
    const progressFill = container.querySelector('.progress-bar-fill');
    const progressPercentage = container.querySelector('.progress-percentage');
    const progressStatus = container.querySelector('.progress-status');
    
    if (progressFill) progressFill.style.width = `${percentage}%`;
    if (progressPercentage) progressPercentage.textContent = `${percentage}%`;
    if (progressStatus) progressStatus.textContent = status;
}

function completeProgress(container) {
    const progressTitle = container.querySelector('.progress-title');
    const progressControls = container.querySelector('.progress-controls');
    
    if (progressTitle) {
        progressTitle.innerHTML = `
            <i class="fas fa-check-circle me-2 text-success"></i>
            تم الانتهاء بنجاح
        `;
    }
    
    if (progressControls) {
        progressControls.innerHTML = `
            <button class="btn btn-success" onclick="closeProgress()">
                <i class="fas fa-check me-2"></i>
                إغلاق
            </button>
        `;
    }
}

function errorProgress(container, message) {
    const progressTitle = container.querySelector('.progress-title');
    const progressStatus = container.querySelector('.progress-status');
    const progressControls = container.querySelector('.progress-controls');
    
    if (progressTitle) {
        progressTitle.innerHTML = `
            <i class="fas fa-exclamation-circle me-2 text-danger"></i>
            حدث خطأ
        `;
    }
    
    if (progressStatus) {
        progressStatus.textContent = message;
        progressStatus.style.color = 'var(--danger-color)';
    }
    
    if (progressControls) {
        progressControls.innerHTML = `
            <button class="btn btn-primary" onclick="retryProgress()">
                <i class="fas fa-redo me-2"></i>
                إعادة المحاولة
            </button>
            <button class="btn btn-secondary" onclick="closeProgress()">
                <i class="fas fa-times me-2"></i>
                إغلاق
            </button>
        `;
    }
}

function pauseProgress() {
    showNotification('تم الإيقاف', 'تم إيقاف العملية مؤقتاً', 'warning');
}

function cancelProgress() {
    showNotification('تم الإلغاء', 'تم إلغاء العملية', 'error');
}

function retryProgress() {
    showNotification('إعادة المحاولة', 'جاري إعادة تشغيل العملية...', 'info');
}

function closeProgress() {
    const progressContainer = document.querySelector('.progress-container');
    if (progressContainer) {
        progressContainer.style.display = 'none';
    }
}

// Contact form handling
function initializeContactForm() {
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactSubmit);
    }
}

function handleContactSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData);
    
    // Show loading
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalContent = showLoading(submitBtn);
    
    // Simulate form submission
    setTimeout(() => {
        hideLoading(submitBtn, originalContent);
        showNotification(
            'تم الإرسال بنجاح',
            'شكراً لتواصلك معنا. سنقوم بالرد عليك في أقرب وقت ممكن.',
            'success'
        );
        e.target.reset();
    }, 2000);
}

// Initialize contact form when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeContactForm();
});

// Make global variables accessible
window.currentPage = currentPage;
window.currentFilters = currentFilters;
window.itemsPerPage = itemsPerPage;

// Export functions for use in other modules
window.DatabaseStore = {
    showNotification,
    formatPrice,
    formatNumber,
    createProgressBar,
    scrollToSection,
    showLoading,
    hideLoading,
    currentPage: () => currentPage,
    currentFilters: () => currentFilters,
    setCurrentPage: (page) => { currentPage = page; window.currentPage = page; },
    setCurrentFilters: (filters) => { currentFilters = filters; window.currentFilters = filters; }
};
