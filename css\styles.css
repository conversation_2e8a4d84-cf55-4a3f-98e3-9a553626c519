/* ===== CSS Variables ===== */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    --border-color: #e2e8f0;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-in-out;
}

/* ===== Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* ===== Header ===== */
.header-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color) !important;
    text-decoration: none;
    transition: var(--transition-fast);
}

.navbar-brand:hover {
    color: var(--primary-dark) !important;
    transform: translateY(-1px);
}

.nav-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* ===== Main Content ===== */
.main-content {
    padding: 3rem 0;
    min-height: calc(100vh - 200px);
}

.hero-section {
    padding: 2rem 0;
    margin-bottom: 3rem;
}

/* ===== Cards ===== */
.generator-card,
.progress-card,
.results-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition);
}

.generator-card:hover,
.results-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1.5rem;
    border-bottom: none;
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 2rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* ===== Form Styles ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: block;
    font-size: 0.95rem;
}

.form-control,
.form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition-fast);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-text {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* ===== Buttons ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.btn-generate {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    font-size: 1.1rem;
    padding: 1rem 2rem;
    box-shadow: var(--shadow-md);
}

.btn-generate:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark), #1e40af);
}

.btn-generate:active {
    transform: translateY(0);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline-success {
    border: 2px solid var(--success-color);
    color: var(--success-color);
    background: transparent;
}

.btn-outline-success:hover {
    background: var(--success-color);
    color: white;
}

.btn-outline-info {
    border: 2px solid var(--info-color);
    color: var(--info-color);
    background: transparent;
}

.btn-outline-info:hover {
    background: var(--info-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #b45309;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #b91c1c;
}

/* ===== Progress Section ===== */
.progress-section {
    margin: 2rem 0;
}

.progress-card {
    text-align: center;
    padding: 2rem;
}

.progress-title {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.progress-wrapper {
    margin-bottom: 2rem;
}

.progress {
    height: 12px;
    border-radius: 6px;
    background: var(--bg-tertiary);
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    height: 100%;
    border-radius: 6px;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    color: var(--text-secondary);
    font-weight: 500;
}

.progress-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* ===== Results Section ===== */
.results-section {
    margin-top: 2rem;
}

.content-display {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 2rem;
    min-height: 300px;
    border: 1px solid var(--border-color);
    white-space: pre-wrap;
    font-family: 'Tajawal', sans-serif;
    line-height: 1.8;
    font-size: 1.05rem;
}

/* ===== Footer ===== */
.footer-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-color);
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer-text {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .progress-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 1.5rem 0;
    }
    
    .hero-section {
        padding: 1rem 0;
        margin-bottom: 2rem;
    }
    
    .content-display {
        padding: 1rem;
        font-size: 1rem;
    }
}

/* ===== Animations ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.6s ease-out;
}

/* ===== Loading States ===== */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===== Form Validation ===== */
.is-invalid {
    border-color: var(--danger-color) !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--danger-color);
}

/* ===== Loading Overlay ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.loading-content {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-xl);
    min-width: 200px;
}

.loading-message {
    margin-top: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* ===== Animation Classes ===== */
.animate-in {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== Utility Classes ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: var(--shadow-xl);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color), var(--info-color)) 1;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }
