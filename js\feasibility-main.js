// Feasibility Studies Main JavaScript
// Main functionality for the feasibility studies store

document.addEventListener('DOMContentLoaded', function() {
    initializeFeasibilityStore();
});

function initializeFeasibilityStore() {
    // Wait for data to be loaded
    if (!window.feasibilityDataLoader || !window.feasibilityDataLoader.data) {
        setTimeout(initializeFeasibilityStore, 100);
        return;
    }

    loadCategories();
    loadFeaturedStudies();
    initializeEventListeners();
    initializeCart();
}

// Global function to be called when data is loaded
window.initializeFeasibilityDisplay = function() {
    loadCategories();
    loadFeaturedStudies();
};

function loadCategories() {
    const categoriesGrid = document.getElementById('categoriesGrid');
    if (!categoriesGrid) return;

    const categories = window.feasibilityDataLoader.getAllCategories();
    categoriesGrid.innerHTML = '';

    Object.keys(categories).forEach(categoryId => {
        const category = categories[categoryId];
        const categoryCard = createCategoryCard(categoryId, category);
        categoriesGrid.appendChild(categoryCard);
    });
}

function createCategoryCard(categoryId, category) {
    const card = document.createElement('div');
    card.className = 'category-card';
    card.innerHTML = `
        <div class="category-icon">
            <i class="${category.icon}"></i>
        </div>
        <h3 class="category-title">${category.name}</h3>
        <p class="category-description">${category.description}</p>
        <div class="category-stats">
            <span class="studies-count">${category.specializations ? category.specializations.length : 0} دراسة متاحة</span>
        </div>
        <button class="btn btn-outline-primary category-btn" onclick="showCategoryStudies('${categoryId}')">
            <i class="fas fa-arrow-left me-2"></i>
            تصفح الدراسات
        </button>
    `;
    return card;
}

function loadFeaturedStudies() {
    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;

    const studies = window.feasibilityDataLoader.getFeaturedStudies(6);
    productsGrid.innerHTML = '';

    studies.forEach(study => {
        const studyCard = createStudyCard(study);
        productsGrid.appendChild(studyCard);
    });
}

function createStudyCard(study) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.innerHTML = `
        <div class="product-header">
            <div class="product-category">
                <i class="${study.categoryIcon}"></i>
                <span>${study.categoryName}</span>
            </div>
            <div class="product-price">${window.feasibilityDataLoader.formatPrice(study.price)}</div>
        </div>
        <div class="product-content">
            <h3 class="product-title">${study.name}</h3>
            <p class="product-description">${study.description}</p>
            <div class="product-details">
                <div class="detail-item">
                    <i class="fas fa-clock"></i>
                    <span>مدة التسليم: ${study.duration}</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${study.locations ? study.locations.slice(0, 2).join(', ') : 'جميع المناطق'}</span>
                </div>
            </div>
            <div class="product-includes">
                <h4>تشمل الدراسة:</h4>
                <ul>
                    ${study.includes ? study.includes.slice(0, 3).map(item => `<li>${item}</li>`).join('') : ''}
                </ul>
            </div>
        </div>
        <div class="product-actions">
            <button class="btn btn-primary" onclick="addToCart('${study.id}')">
                <i class="fas fa-cart-plus me-2"></i>
                إضافة للسلة
            </button>
            <button class="btn btn-outline-primary" onclick="showStudyDetails('${study.id}')">
                <i class="fas fa-eye me-2"></i>
                التفاصيل
            </button>
        </div>
    `;
    return card;
}

function showCategoryStudies(categoryId) {
    const studies = window.feasibilityDataLoader.getStudiesByCategory(categoryId);
    const productsGrid = document.getElementById('productsGrid');
    
    if (!productsGrid) return;

    productsGrid.innerHTML = '';
    studies.forEach(study => {
        const studyCard = createStudyCard(study);
        productsGrid.appendChild(studyCard);
    });

    // Scroll to products section
    scrollToSection('products');
}

function showStudyDetails(studyId) {
    const study = window.feasibilityDataLoader.getStudyById(studyId);
    if (!study) return;

    const modalTitle = document.getElementById('productModalTitle');
    const modalBody = document.getElementById('productModalBody');

    if (modalTitle) modalTitle.textContent = study.name;
    if (modalBody) {
        modalBody.innerHTML = `
            <div class="study-details">
                <div class="row">
                    <div class="col-md-6">
                        <h4>معلومات الدراسة</h4>
                        <div class="detail-group">
                            <label>السعر:</label>
                            <span class="price">${window.feasibilityDataLoader.formatPrice(study.price)}</span>
                        </div>
                        <div class="detail-group">
                            <label>مدة التسليم:</label>
                            <span>${study.duration}</span>
                        </div>
                        <div class="detail-group">
                            <label>المناطق المتاحة:</label>
                            <span>${study.locations ? study.locations.join(', ') : 'جميع المناطق'}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h4>تشمل الدراسة</h4>
                        <ul class="includes-list">
                            ${study.includes ? study.includes.map(item => `<li><i class="fas fa-check"></i> ${item}</li>`).join('') : ''}
                        </ul>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-12">
                        <h4>وصف الدراسة</h4>
                        <p>${study.description}</p>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-12">
                        <button class="btn btn-primary btn-lg" onclick="addToCart('${study.id}'); bootstrap.Modal.getInstance(document.getElementById('productModal')).hide();">
                            <i class="fas fa-cart-plus me-2"></i>
                            إضافة للسلة - ${window.feasibilityDataLoader.formatPrice(study.price)}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
}

function initializeEventListeners() {
    // Search functionality
    const searchBtn = document.getElementById('searchBtn');
    const searchInput = document.getElementById('searchInput');
    
    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
    }
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    // Load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreStudies);
    }

    // Cart icon click
    const cartIcon = document.getElementById('cartIcon');
    if (cartIcon) {
        cartIcon.addEventListener('click', showCart);
    }
}

function performSearch() {
    const searchInput = document.getElementById('searchInput');
    const categoryFilter = document.getElementById('categoryFilter');
    const priceFilter = document.getElementById('priceFilter');

    const query = searchInput ? searchInput.value : '';
    const category = categoryFilter ? categoryFilter.value : '';
    const price = priceFilter ? priceFilter.value : '';

    const results = window.feasibilityDataLoader.searchStudies(query, category, price);
    displaySearchResults(results);
}

function displaySearchResults(results) {
    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;

    productsGrid.innerHTML = '';

    if (results.length === 0) {
        productsGrid.innerHTML = `
            <div class="col-12 text-center">
                <div class="no-results">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <h3>لم يتم العثور على نتائج</h3>
                    <p>جرب البحث بكلمات مختلفة أو تغيير الفلاتر</p>
                </div>
            </div>
        `;
        return;
    }

    results.forEach(study => {
        const studyCard = createStudyCard(study);
        productsGrid.appendChild(studyCard);
    });

    // Scroll to results
    scrollToSection('products');
}

function loadMoreStudies() {
    // This would typically load more studies from the server
    // For now, we'll just show all studies
    const allStudies = window.feasibilityDataLoader.getAllStudies();
    const productsGrid = document.getElementById('productsGrid');
    
    if (!productsGrid) return;

    productsGrid.innerHTML = '';
    allStudies.forEach(study => {
        const studyCard = createStudyCard(study);
        productsGrid.appendChild(studyCard);
    });

    // Hide the load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.style.display = 'none';
    }
}

function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({ behavior: 'smooth' });
    }
}

// Initialize cart functionality
function initializeCart() {
    if (!localStorage.getItem('feasibilityCart')) {
        localStorage.setItem('feasibilityCart', JSON.stringify([]));
    }
    updateCartCount();
}

function addToCart(studyId) {
    const study = window.feasibilityDataLoader.getStudyById(studyId);
    if (!study) return;

    let cart = JSON.parse(localStorage.getItem('feasibilityCart') || '[]');
    
    // Check if study already exists in cart
    const existingIndex = cart.findIndex(item => item.id === studyId);
    
    if (existingIndex > -1) {
        // Study already in cart, show message
        showNotification('هذه الدراسة موجودة بالفعل في السلة', 'warning');
        return;
    }

    // Add study to cart
    cart.push({
        id: study.id,
        name: study.name,
        price: study.price,
        category: study.categoryName,
        duration: study.duration
    });

    localStorage.setItem('feasibilityCart', JSON.stringify(cart));
    updateCartCount();
    showNotification('تم إضافة الدراسة إلى السلة بنجاح', 'success');
}

function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem('feasibilityCart') || '[]');
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.textContent = cart.length;
    }
}

function showCart() {
    // This would show the cart modal
    const modal = new bootstrap.Modal(document.getElementById('cartModal'));
    modal.show();
}

function showNotification(message, type = 'info') {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
