// ===== UI Components and Utilities =====

class UIComponents {
    static init() {
        this.createToastContainer();
        this.setupGlobalEventListeners();
        this.initializeAnimations();
    }

    static createToastContainer() {
        if (!document.getElementById('toast-container')) {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
    }

    static showToast(title, message, type = 'info', duration = 5000) {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <div class="toast-icon">
                <i class="${icons[type] || icons.info}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        container.appendChild(toast);

        // إزالة التوست تلقائياً
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => toast.remove(), 300);
            }
        }, duration);

        return toast;
    }

    static showModal(title, content, options = {}) {
        const {
            size = 'medium',
            showCloseButton = true,
            backdrop = true,
            keyboard = true
        } = options;

        // إنشاء الخلفية
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        
        if (backdrop) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.closeModal(overlay);
                }
            });
        }

        // إنشاء المحتوى
        const modal = document.createElement('div');
        modal.className = `modal-content modal-${size}`;
        
        modal.innerHTML = `
            <div class="modal-header">
                <h3 class="modal-title">${title}</h3>
                ${showCloseButton ? '<button class="modal-close" onclick="UIComponents.closeModal(this.closest(\'.modal-overlay\'))"><i class="fas fa-times"></i></button>' : ''}
            </div>
            <div class="modal-body">
                ${content}
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // إضافة دعم لوحة المفاتيح
        if (keyboard) {
            const handleKeydown = (e) => {
                if (e.key === 'Escape') {
                    this.closeModal(overlay);
                    document.removeEventListener('keydown', handleKeydown);
                }
            };
            document.addEventListener('keydown', handleKeydown);
        }

        return overlay;
    }

    static closeModal(modal) {
        if (modal) {
            modal.style.opacity = '0';
            setTimeout(() => modal.remove(), 300);
        }
    }

    static showLoading(message = 'جاري التحميل...', size = 'medium') {
        const loading = document.createElement('div');
        loading.className = 'loading-overlay';
        loading.innerHTML = `
            <div class="loading-content">
                <div class="spinner spinner-${size}"></div>
                <div class="loading-message">${message}</div>
            </div>
        `;
        
        document.body.appendChild(loading);
        return loading;
    }

    static hideLoading(loadingElement) {
        if (loadingElement && loadingElement.parentElement) {
            loadingElement.remove();
        }
    }

    static createAccordion(items, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        
        items.forEach((item, index) => {
            const accordionItem = document.createElement('div');
            accordionItem.className = 'accordion-item';
            accordionItem.innerHTML = `
                <div class="accordion-header" onclick="UIComponents.toggleAccordion(this)">
                    <h4 class="accordion-title">${item.title}</h4>
                    <i class="accordion-icon fas fa-chevron-down"></i>
                </div>
                <div class="accordion-content">
                    <div class="accordion-body">
                        ${item.content}
                    </div>
                </div>
            `;
            
            container.appendChild(accordionItem);
        });
    }

    static toggleAccordion(header) {
        const item = header.parentElement;
        const isActive = item.classList.contains('active');
        
        // إغلاق جميع العناصر الأخرى
        const allItems = item.parentElement.querySelectorAll('.accordion-item');
        allItems.forEach(otherItem => {
            if (otherItem !== item) {
                otherItem.classList.remove('active');
            }
        });
        
        // تبديل العنصر الحالي
        item.classList.toggle('active', !isActive);
    }

    static createTabs(tabs, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const tabsNav = document.createElement('div');
        tabsNav.className = 'tabs-nav';
        
        const tabsContent = document.createElement('div');
        tabsContent.className = 'tabs-content-container';

        tabs.forEach((tab, index) => {
            // إنشاء زر التبويب
            const tabButton = document.createElement('button');
            tabButton.className = `tab-button ${index === 0 ? 'active' : ''}`;
            tabButton.textContent = tab.title;
            tabButton.onclick = () => this.switchTab(index, containerId);
            tabsNav.appendChild(tabButton);

            // إنشاء محتوى التبويب
            const tabContent = document.createElement('div');
            tabContent.className = `tab-content ${index === 0 ? 'active' : ''}`;
            tabContent.innerHTML = tab.content;
            tabsContent.appendChild(tabContent);
        });

        container.innerHTML = '';
        container.appendChild(tabsNav);
        container.appendChild(tabsContent);
    }

    static switchTab(activeIndex, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const buttons = container.querySelectorAll('.tab-button');
        const contents = container.querySelectorAll('.tab-content');

        buttons.forEach((button, index) => {
            button.classList.toggle('active', index === activeIndex);
        });

        contents.forEach((content, index) => {
            content.classList.toggle('active', index === activeIndex);
        });
    }

    static createProgressBar(containerId, options = {}) {
        const {
            value = 0,
            max = 100,
            showPercentage = true,
            animated = true,
            color = 'primary'
        } = options;

        const container = document.getElementById(containerId);
        if (!container) return;

        const progressWrapper = document.createElement('div');
        progressWrapper.className = 'progress-wrapper';
        
        const progressBar = document.createElement('div');
        progressBar.className = 'progress';
        
        const progressFill = document.createElement('div');
        progressFill.className = `progress-bar progress-bar-${color} ${animated ? 'progress-bar-animated' : ''}`;
        progressFill.style.width = `${(value / max) * 100}%`;
        
        progressBar.appendChild(progressFill);
        progressWrapper.appendChild(progressBar);
        
        if (showPercentage) {
            const percentage = document.createElement('div');
            percentage.className = 'progress-percentage';
            percentage.textContent = `${Math.round((value / max) * 100)}%`;
            progressWrapper.appendChild(percentage);
        }
        
        container.appendChild(progressWrapper);
        
        return {
            update: (newValue) => {
                const percentage = Math.round((newValue / max) * 100);
                progressFill.style.width = `${percentage}%`;
                if (showPercentage) {
                    container.querySelector('.progress-percentage').textContent = `${percentage}%`;
                }
            }
        };
    }

    static createBadge(text, type = 'primary') {
        const badge = document.createElement('span');
        badge.className = `badge badge-${type}`;
        badge.textContent = text;
        return badge;
    }

    static formatContent(content) {
        // تنسيق المحتوى للعرض
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/^(?!<p>)/, '<p>')
            .replace(/(?!<\/p>)$/, '</p>');
    }

    static copyToClipboard(text) {
        return navigator.clipboard.writeText(text).then(() => {
            this.showToast('تم النسخ', 'تم نسخ النص إلى الحافظة', 'success');
            return true;
        }).catch(err => {
            console.error('خطأ في النسخ:', err);
            this.showToast('خطأ', 'فشل في نسخ النص', 'error');
            return false;
        });
    }

    static downloadFile(content, filename, type = 'text/plain') {
        const blob = new Blob([content], { type });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        
        this.showToast('تم التحميل', `تم تحميل الملف: ${filename}`, 'success');
    }

    static setupGlobalEventListeners() {
        // إضافة مستمعي الأحداث العامة
        document.addEventListener('click', (e) => {
            // إغلاق القوائم المنسدلة عند النقر خارجها
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });

        // دعم اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            // Ctrl+S للحفظ
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.showToast('حفظ', 'تم حفظ التغييرات', 'success');
            }
            
            // Escape لإغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal-overlay');
                modals.forEach(modal => this.closeModal(modal));
            }
        });
    }

    static initializeAnimations() {
        // إضافة تأثيرات الحركة عند التمرير
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // مراقبة العناصر القابلة للحركة
        document.querySelectorAll('.card, .feature-card, .stat-card').forEach(el => {
            observer.observe(el);
        });
    }

    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    static validatePhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    }

    static formatNumber(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    }

    static formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        return new Intl.DateTimeFormat('ar-SA', { ...defaultOptions, ...options }).format(new Date(date));
    }

    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    static smoothScrollTo(element, duration = 1000) {
        const targetPosition = element.offsetTop;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        let startTime = null;

        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const run = ease(timeElapsed, startPosition, distance, duration);
            window.scrollTo(0, run);
            if (timeElapsed < duration) requestAnimationFrame(animation);
        }

        function ease(t, b, c, d) {
            t /= d / 2;
            if (t < 1) return c / 2 * t * t + b;
            t--;
            return -c / 2 * (t * (t - 2) - 1) + b;
        }

        requestAnimationFrame(animation);
    }
}

// تهيئة المكونات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    UIComponents.init();
});
